{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "add_deprecated_target_alias", "add_dependencies"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/utilities.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 84, "parent": 0}, {"command": 0, "file": 0, "line": 359, "parent": 1}, {"command": 2, "file": 0, "line": 364, "parent": 1}]}, "dependencies": [{"backtrace": 3, "id": "efuse-custom-table::@2da764c922f8b73e9c75"}], "id": "efuse_custom_table::@2da764c922f8b73e9c75", "name": "efuse_custom_table", "paths": {"build": "esp-idf/efuse", "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/esp-idf/efuse/CMakeFiles/efuse_custom_table", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/esp-idf/efuse/CMakeFiles/efuse_custom_table.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}