{"archive": {}, "artifacts": [{"path": "esp-idf/heap/libheap.a"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_library", "idf_component_register", "add_compile_options", "add_c_compile_options", "target_compile_options", "add_compile_definitions", "include_directories", "target_include_directories", "__component_add_include_dirs"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/component.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/utilities.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 54, "parent": 0}, {"command": 0, "file": 0, "line": 491, "parent": 1}, {"command": 2, "file": 0, "line": 476, "parent": 1}, {"command": 3, "file": 0, "line": 478, "parent": 1}, {"command": 2, "file": 2, "line": 281, "parent": 4}, {"command": 4, "file": 1, "line": 75, "parent": 0}, {"command": 5, "file": 0, "line": 477, "parent": 1}, {"command": 6, "file": 0, "line": 475, "parent": 1}, {"command": 8, "file": 0, "line": 493, "parent": 1}, {"command": 7, "file": 0, "line": 320, "parent": 9}, {"command": 7, "file": 0, "line": 320, "parent": 9}, {"command": 8, "file": 0, "line": 494, "parent": 1}, {"command": 7, "file": 0, "line": 320, "parent": 12}, {"command": 7, "file": 0, "line": 320, "parent": 12}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always"}, {"backtrace": 3, "fragment": "-ffunction-sections"}, {"backtrace": 3, "fragment": "-fdata-sections"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Werror=all"}, {"backtrace": 3, "fragment": "-Wno-error=unused-function"}, {"backtrace": 3, "fragment": "-Wno-error=unused-variable"}, {"backtrace": 3, "fragment": "-Wno-error=unused-but-set-variable"}, {"backtrace": 3, "fragment": "-Wno-error=deprecated-declarations"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wno-error=extra"}, {"backtrace": 3, "fragment": "-Wno-unused-parameter"}, {"backtrace": 3, "fragment": "-Wno-sign-compare"}, {"backtrace": 3, "fragment": "-Wno-enum-conversion"}, {"backtrace": 3, "fragment": "-gdwarf-4"}, {"backtrace": 3, "fragment": "-ggdb"}, {"backtrace": 3, "fragment": "-nostartfiles"}, {"backtrace": 3, "fragment": "-Og"}, {"backtrace": 3, "fragment": "-fno-shrink-wrap"}, {"backtrace": 3, "fragment": "-fmacro-prefix-map=C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver=."}, {"backtrace": 3, "fragment": "-fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf=/IDF"}, {"backtrace": 3, "fragment": "-fstrict-volatile-bitfields"}, {"backtrace": 3, "fragment": "-fno-jump-tables"}, {"backtrace": 3, "fragment": "-fno-tree-switch-conversion"}, {"backtrace": 5, "fragment": "-std=gnu17"}, {"backtrace": 5, "fragment": "-Wno-old-style-declaration"}, {"backtrace": 6, "fragment": "-DMULTI_HEAP_FREERTOS"}], "defines": [{"backtrace": 7, "define": "ESP_PLATFORM"}, {"backtrace": 7, "define": "IDF_VER=\"5.5.0\""}, {"backtrace": 2, "define": "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE"}, {"backtrace": 2, "define": "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ"}, {"backtrace": 7, "define": "_GLIBCXX_HAVE_POSIX_SEMAPHORE"}, {"backtrace": 7, "define": "_GLIBCXX_USE_POSIX_SEMAPHORE"}, {"backtrace": 7, "define": "_GNU_SOURCE"}, {"backtrace": 7, "define": "_POSIX_READER_WRITER_LOCKS"}], "includes": [{"backtrace": 8, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/config"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/include"}, {"backtrace": 11, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/tlsf"}, {"backtrace": 13, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/private_include"}, {"backtrace": 14, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/tlsf/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/platform_include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include/freertos"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/riscv/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/riscv/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/riscv/include/freertos"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/esp_additions/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/dma/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/ldo/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/debug_probe/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/power_supply/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/."}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/private_include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/log/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/register"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/platform_port/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/soc"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/include/riscv"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/include/private"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/riscv/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps/sntp"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/lwip/src/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/freertos/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/arch"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/sys"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "dependencies": [{"id": "__idf_soc::@824f1541829f13857909"}], "id": "__idf_heap::@a3bf99b7c47be461ad9f", "name": "__idf_heap", "nameOnDisk": "libheap.a", "paths": {"build": "esp-idf/heap", "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/heap_caps_base.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/heap_caps.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/heap_caps_init.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/multi_heap.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/port/memory_layout_utils.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/port/esp32c6/memory_layout.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}