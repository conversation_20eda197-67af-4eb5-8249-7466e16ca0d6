{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [87, 88, 90, 93, 95, 104, 106, 107, 109, 110, 116, 117, 118, 125, 129, 130, 131, 132, 133]}, {"build": "esp-idf", "childIndexes": [2, 3, 4, 5, 6, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119], "hasInstallRule": true, "jsonFile": "directory-esp-idf-4ce53965c45cd09d05cc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf"}, {"build": "esp-idf/riscv", "jsonFile": "directory-esp-idf.riscv-c81ec38e3e1513f4dddd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/riscv", "targetIndexes": [74]}, {"build": "esp-idf/esp_driver_gpio", "jsonFile": "directory-esp-idf.esp_driver_gpio-e0669eee4381b0ebb539.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_gpio", "targetIndexes": [16]}, {"build": "esp-idf/esp_timer", "jsonFile": "directory-esp-idf.esp_timer-1df2fe894b250ed553fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_timer", "targetIndexes": [53]}, {"build": "esp-idf/esp_pm", "jsonFile": "directory-esp-idf.esp_pm-b902617d9fe809be9e59.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_pm", "targetIndexes": [48]}, {"build": "esp-idf/mbedtls", "childIndexes": [7], "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls-1ae3e2b94b2576f893eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls", "targetIndexes": [66, 96]}, {"build": "esp-idf/mbedtls/mbedtls", "childIndexes": [8, 9, 12, 13], "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls-761ba55bfc503e740c4f.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 6, "projectIndex": 2, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls", "targetIndexes": [89]}, {"build": "esp-idf/mbedtls/mbedtls/include", "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls.include-fc90834274703ef6168e.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 7, "projectIndex": 2, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/include"}, {"build": "esp-idf/mbedtls/mbedtls/3rdparty", "childIndexes": [10, 11], "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls.3rdparty-84376116fc5ab22f09b1.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 7, "projectIndex": 2, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty"}, {"build": "esp-idf/mbedtls/mbedtls/3rdparty/everest", "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls.3rdparty.everest-9a296c87f3032136f4e1.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 9, "projectIndex": 2, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/everest", "targetIndexes": [108]}, {"build": "esp-idf/mbedtls/mbedtls/3rdparty/p256-m", "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls.3rdparty.p256-m-e83e59d6c17b56b0dae7.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 9, "projectIndex": 2, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/p256-m", "targetIndexes": [119]}, {"build": "esp-idf/mbedtls/mbedtls/library", "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls.library-22d445df23bb988b6743.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 7, "projectIndex": 2, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/library", "targetIndexes": [111, 112, 113, 114]}, {"build": "esp-idf/mbedtls/mbedtls/pkgconfig", "jsonFile": "directory-esp-idf.mbedtls.mbedtls.pkgconfig-065328ce1aa5b126a24c.json", "minimumCMakeVersion": {"string": "3.10.2"}, "parentIndex": 7, "projectIndex": 2, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/pkgconfig"}, {"build": "esp-idf/bootloader", "jsonFile": "directory-esp-idf.bootloader-f2802f5c3531041690b5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader", "targetIndexes": [94, 103]}, {"build": "esp-idf/esptool_py", "jsonFile": "directory-esp-idf.esptool_py-d2e51924413bad0b4d2b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py", "targetIndexes": [91, 92, 102]}, {"build": "esp-idf/partition_table", "jsonFile": "directory-esp-idf.partition_table-1925719c98c8b5fd8ce7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/partition_table", "targetIndexes": [105, 120, 121, 122, 123, 124]}, {"build": "esp-idf/esp_app_format", "jsonFile": "directory-esp-idf.esp_app_format-8ea29cb05bcecbeae4ad.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_app_format", "targetIndexes": [11]}, {"build": "esp-idf/esp_bootloader_format", "jsonFile": "directory-esp-idf.esp_bootloader_format-7b67763630a0df99a6c9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_bootloader_format", "targetIndexes": [12]}, {"build": "esp-idf/app_update", "jsonFile": "directory-esp-idf.app_update-854533467560d4bbcf17.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/app_update", "targetIndexes": [2]}, {"build": "esp-idf/esp_partition", "jsonFile": "directory-esp-idf.esp_partition-d65a75b7cf634f644eec.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_partition", "targetIndexes": [46]}, {"build": "esp-idf/efuse", "jsonFile": "directory-esp-idf.efuse-8822af0d1d07f1bf0039.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse", "targetIndexes": [8, 97, 98, 99, 100, 101, 127, 128]}, {"build": "esp-idf/bootloader_support", "jsonFile": "directory-esp-idf.bootloader_support-21284ae9f949cf8c44e2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support", "targetIndexes": [3]}, {"build": "esp-idf/esp_mm", "jsonFile": "directory-esp-idf.esp_mm-84365430f12f25d7cba9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_mm", "targetIndexes": [44]}, {"build": "esp-idf/spi_flash", "jsonFile": "directory-esp-idf.spi_flash-68ce979caab053ea2373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash", "targetIndexes": [78]}, {"build": "esp-idf/esp_system", "childIndexes": [26], "jsonFile": "directory-esp-idf.esp_system-e4254296185676bee716.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system", "targetIndexes": [52, 115, 126]}, {"build": "esp-idf/esp_system/port", "childIndexes": [27], "jsonFile": "directory-esp-idf.esp_system.port-ece5edf84182f66f2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 25, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port"}, {"build": "esp-idf/esp_system/port/soc/esp32c6", "jsonFile": "directory-esp-idf.esp_system.port.soc.esp32c6-851fa6bbbe47a3b0f3dd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 26, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/soc/esp32c6"}, {"build": "esp-idf/esp_common", "jsonFile": "directory-esp-idf.esp_common-7842d604b4865c5a55ca.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common", "targetIndexes": [14]}, {"build": "esp-idf/esp_rom", "jsonFile": "directory-esp-idf.esp_rom-2dc702ce4d5e9527a534.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom", "targetIndexes": [50]}, {"build": "esp-idf/hal", "jsonFile": "directory-esp-idf.hal-bef9d6bec9341a508ffa.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal", "targetIndexes": [59]}, {"build": "esp-idf/log", "jsonFile": "directory-esp-idf.log-0eda141f6cf94a6441cd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/log", "targetIndexes": [64]}, {"build": "esp-idf/heap", "jsonFile": "directory-esp-idf.heap-3b7688a1531f0802af0e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap", "targetIndexes": [60]}, {"build": "esp-idf/soc", "jsonFile": "directory-esp-idf.soc-b2ac3d6eaf3b150286a8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc", "targetIndexes": [77]}, {"build": "esp-idf/esp_security", "jsonFile": "directory-esp-idf.esp_security-026b18d19f7b30dcf270.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_security", "targetIndexes": [51]}, {"build": "esp-idf/esp_hw_support", "childIndexes": [36, 37], "jsonFile": "directory-esp-idf.esp_hw_support-dc33cff4e7749e06e3b2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support", "targetIndexes": [41]}, {"build": "esp-idf/esp_hw_support/port/esp32c6", "jsonFile": "directory-esp-idf.esp_hw_support.port.esp32c6-4359866ad5b853018675.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 35, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6"}, {"build": "esp-idf/esp_hw_support/lowpower", "jsonFile": "directory-esp-idf.esp_hw_support.lowpower-50746ddbf0ab606033e2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 35, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/lowpower"}, {"build": "esp-idf/freertos", "jsonFile": "directory-esp-idf.freertos-6ec356af6cab6a318b2f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos", "targetIndexes": [58]}, {"build": "esp-idf/newlib", "childIndexes": [40], "jsonFile": "directory-esp-idf.newlib-697528d976c5859bfdf4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib", "targetIndexes": [68]}, {"build": "esp-idf/newlib/src/port", "jsonFile": "directory-esp-idf.newlib.src.port-be36ba3308ba7bcdfd96.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 39, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/src/port"}, {"build": "esp-idf/pthread", "jsonFile": "directory-esp-idf.pthread-0aa07f61a7697be4a1f9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/pthread", "targetIndexes": [73]}, {"build": "esp-idf/cxx", "jsonFile": "directory-esp-idf.cxx-4abad1532b5d6801ca9b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/cxx", "targetIndexes": [6]}, {"build": "esp-idf/__pio_env", "jsonFile": "directory-esp-idf.__pio_env-d361a84c48a5c319264f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/__pio_env", "targetIndexes": [0]}, {"build": "esp-idf/esp_driver_gptimer", "jsonFile": "directory-esp-idf.esp_driver_gptimer-da555269ddc05edf5c8a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_gptimer", "targetIndexes": [17]}, {"build": "esp-idf/esp_ringbuf", "jsonFile": "directory-esp-idf.esp_ringbuf-6e2544ff43a51e11073d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_ringbuf", "targetIndexes": [49]}, {"build": "esp-idf/esp_driver_uart", "jsonFile": "directory-esp-idf.esp_driver_uart-23c3d431a3a5db94639e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_uart", "targetIndexes": [31]}, {"build": "esp-idf/app_trace", "jsonFile": "directory-esp-idf.app_trace-6a910216893ff61e9b05.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/app_trace", "targetIndexes": [1]}, {"build": "esp-idf/esp_event", "jsonFile": "directory-esp-idf.esp_event-2c59788ddfe38f656437.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_event", "targetIndexes": [34]}, {"build": "esp-idf/nvs_flash", "jsonFile": "directory-esp-idf.nvs_flash-7ae86a2400a036ecb93f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/nvs_flash", "targetIndexes": [69]}, {"build": "esp-idf/esp_phy", "jsonFile": "directory-esp-idf.esp_phy-5c4b28f9fe4113bb2bb0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_phy", "targetIndexes": [47]}, {"build": "esp-idf/esp_driver_usb_serial_jtag", "jsonFile": "directory-esp-idf.esp_driver_usb_serial_jtag-38522fdace6c1399216b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_usb_serial_jtag", "targetIndexes": [32]}, {"build": "esp-idf/esp_vfs_console", "jsonFile": "directory-esp-idf.esp_vfs_console-cecc51d7b1ee7dff8a15.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_vfs_console", "targetIndexes": [54]}, {"build": "esp-idf/vfs", "jsonFile": "directory-esp-idf.vfs-e04a15bc3dc096a5c9cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/vfs", "targetIndexes": [83]}, {"build": "esp-idf/lwip", "jsonFile": "directory-esp-idf.lwip-0723cf184f91aaacb362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip", "targetIndexes": [65]}, {"build": "esp-idf/esp_netif_stack", "jsonFile": "directory-esp-idf.esp_netif_stack-d7562618a541c7c9a825.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_netif_stack"}, {"build": "esp-idf/esp_netif", "jsonFile": "directory-esp-idf.esp_netif-e0e7db67c6a00cda8c43.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_netif", "targetIndexes": [45]}, {"build": "esp-idf/wpa_supplicant", "jsonFile": "directory-esp-idf.wpa_supplicant-58b1ab0a75d252641ef0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/wpa_supplicant", "targetIndexes": [86]}, {"build": "esp-idf/esp_coex", "jsonFile": "directory-esp-idf.esp_coex-5c256ae706d298ba6018.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_coex", "targetIndexes": [13]}, {"build": "esp-idf/esp_wifi", "jsonFile": "directory-esp-idf.esp_wifi-efc65f40108fb706646a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi", "targetIndexes": [55]}, {"build": "esp-idf/esp_driver_spi", "jsonFile": "directory-esp-idf.esp_driver_spi-7eaa90e978af59aa1ef7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_spi", "targetIndexes": [28]}, {"build": "esp-idf/esp_gdbstub", "jsonFile": "directory-esp-idf.esp_gdbstub-53b06e407a63a7da514a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_gdbstub", "targetIndexes": [35]}, {"build": "esp-idf/bt", "jsonFile": "directory-esp-idf.bt-b458e9dfd658227087cd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bt"}, {"build": "esp-idf/unity", "jsonFile": "directory-esp-idf.unity-50f6162b5a2f9dd70d99.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/unity", "targetIndexes": [82]}, {"build": "esp-idf/cmock", "jsonFile": "directory-esp-idf.cmock-7d878835c189935a09ba.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/cmock", "targetIndexes": [4]}, {"build": "esp-idf/console", "jsonFile": "directory-esp-idf.console-bfe4aca14f6e8eea0d1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/console", "targetIndexes": [5]}, {"build": "esp-idf/esp_driver_pcnt", "jsonFile": "directory-esp-idf.esp_driver_pcnt-6b690ff3d93e5758015e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_pcnt", "targetIndexes": [23]}, {"build": "esp-idf/esp_driver_mcpwm", "jsonFile": "directory-esp-idf.esp_driver_mcpwm-d155115826ad348a5279.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_mcpwm", "targetIndexes": [21]}, {"build": "esp-idf/esp_driver_ana_cmpr", "jsonFile": "directory-esp-idf.esp_driver_ana_cmpr-9ea04eb4e0e668153e18.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ana_cmpr"}, {"build": "esp-idf/esp_driver_i2s", "jsonFile": "directory-esp-idf.esp_driver_i2s-294b4d8ff4f781488625.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_i2s", "targetIndexes": [19]}, {"build": "esp-idf/sdmmc", "jsonFile": "directory-esp-idf.sdmmc-a56e88ecb420eb08d63a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/sdmmc", "targetIndexes": [76]}, {"build": "esp-idf/esp_driver_sdmmc", "jsonFile": "directory-esp-idf.esp_driver_sdmmc-0df39ad9dc1051065c07.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdmmc"}, {"build": "esp-idf/esp_driver_sdspi", "jsonFile": "directory-esp-idf.esp_driver_sdspi-42dde919f6470e5f0a14.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdspi", "targetIndexes": [27]}, {"build": "esp-idf/esp_driver_sdio", "jsonFile": "directory-esp-idf.esp_driver_sdio-21f86dd554b1647d9f34.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdio", "targetIndexes": [25]}, {"build": "esp-idf/esp_driver_dac", "jsonFile": "directory-esp-idf.esp_driver_dac-9c35cb455c66a5e63493.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_dac"}, {"build": "esp-idf/esp_driver_bitscrambler", "jsonFile": "directory-esp-idf.esp_driver_bitscrambler-f7824ed889ae895c75d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_bitscrambler"}, {"build": "esp-idf/esp_driver_rmt", "jsonFile": "directory-esp-idf.esp_driver_rmt-8e6a01885f726e11ac61.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_rmt", "targetIndexes": [24]}, {"build": "esp-idf/esp_driver_tsens", "jsonFile": "directory-esp-idf.esp_driver_tsens-03c2839481249a16b878.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_tsens", "targetIndexes": [29]}, {"build": "esp-idf/esp_driver_sdm", "jsonFile": "directory-esp-idf.esp_driver_sdm-75d48773259b6305295a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdm", "targetIndexes": [26]}, {"build": "esp-idf/esp_driver_i2c", "jsonFile": "directory-esp-idf.esp_driver_i2c-284c656eec21eb8429c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_i2c", "targetIndexes": [18]}, {"build": "esp-idf/esp_driver_ledc", "jsonFile": "directory-esp-idf.esp_driver_ledc-9f896d00b56f2e2445bc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ledc", "targetIndexes": [20]}, {"build": "esp-idf/esp_driver_parlio", "jsonFile": "directory-esp-idf.esp_driver_parlio-535e65064143bb5b0d58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_parlio", "targetIndexes": [22]}, {"build": "esp-idf/esp_driver_twai", "jsonFile": "directory-esp-idf.esp_driver_twai-dce0a7fbf1c60f8a067c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_twai", "targetIndexes": [30]}, {"build": "esp-idf/driver", "jsonFile": "directory-esp-idf.driver-98ba97d6909709b860ae.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/driver", "targetIndexes": [7]}, {"build": "esp-idf/http_parser", "jsonFile": "directory-esp-idf.http_parser-a59b5dd538d35660896b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/http_parser", "targetIndexes": [61]}, {"build": "esp-idf/esp-tls", "jsonFile": "directory-esp-idf.esp-tls-65c078aacc603b40eec3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp-tls", "targetIndexes": [9]}, {"build": "esp-idf/esp_adc", "jsonFile": "directory-esp-idf.esp_adc-2f46f26f28258beae137.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc", "targetIndexes": [10]}, {"build": "esp-idf/esp_driver_isp", "jsonFile": "directory-esp-idf.esp_driver_isp-5fa46b95171f574c865f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_isp"}, {"build": "esp-idf/esp_driver_cam", "jsonFile": "directory-esp-idf.esp_driver_cam-2cc39cdc990b3fd65897.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_cam", "targetIndexes": [15]}, {"build": "esp-idf/esp_psram", "jsonFile": "directory-esp-idf.esp_psram-654a88c4e3b14432ca29.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_psram"}, {"build": "esp-idf/esp_driver_jpeg", "jsonFile": "directory-esp-idf.esp_driver_jpeg-cff6a6c9bde0a7185ae9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_jpeg"}, {"build": "esp-idf/esp_driver_ppa", "jsonFile": "directory-esp-idf.esp_driver_ppa-39be0f6b1ae34a6d195c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ppa"}, {"build": "esp-idf/esp_driver_touch_sens", "jsonFile": "directory-esp-idf.esp_driver_touch_sens-d5894ae21ef1231c42b2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_touch_sens"}, {"build": "esp-idf/esp_eth", "jsonFile": "directory-esp-idf.esp_eth-5c059f70c69aa3cc7318.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_eth", "targetIndexes": [33]}, {"build": "esp-idf/esp_hid", "jsonFile": "directory-esp-idf.esp_hid-2f5f4eded6b971fbf938.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hid", "targetIndexes": [36]}, {"build": "esp-idf/tcp_transport", "jsonFile": "directory-esp-idf.tcp_transport-84126c54e6052fa76433.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/tcp_transport", "targetIndexes": [81]}, {"build": "esp-idf/esp_http_client", "jsonFile": "directory-esp-idf.esp_http_client-d6aa446cd80c58dfe640.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_http_client", "targetIndexes": [37]}, {"build": "esp-idf/esp_http_server", "jsonFile": "directory-esp-idf.esp_http_server-4baefa73c6057da12a11.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_http_server", "targetIndexes": [38]}, {"build": "esp-idf/esp_https_ota", "jsonFile": "directory-esp-idf.esp_https_ota-07830f9dfd39fc8b804c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_https_ota", "targetIndexes": [39]}, {"build": "esp-idf/esp_https_server", "jsonFile": "directory-esp-idf.esp_https_server-6480d0f5408164657df9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_https_server", "targetIndexes": [40]}, {"build": "esp-idf/esp_lcd", "jsonFile": "directory-esp-idf.esp_lcd-630b0cca3f978fe4300d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_lcd", "targetIndexes": [42]}, {"build": "esp-idf/protobuf-c", "jsonFile": "directory-esp-idf.protobuf-c-c2989593653a0bfcca5d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protobuf-c", "targetIndexes": [71]}, {"build": "esp-idf/protocomm", "jsonFile": "directory-esp-idf.protocomm-811825f0e6d5bb9c229a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm", "targetIndexes": [72]}, {"build": "esp-idf/esp_local_ctrl", "jsonFile": "directory-esp-idf.esp_local_ctrl-3bbcaee5b64bbea8cd06.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl", "targetIndexes": [43]}, {"build": "esp-idf/esp_tee", "jsonFile": "directory-esp-idf.esp_tee-df36a07b609fe93636b7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_tee"}, {"build": "esp-idf/espcoredump", "jsonFile": "directory-esp-idf.espcoredump-6c9a72a70e99d97fd3d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/espcoredump", "targetIndexes": [56]}, {"build": "esp-idf/wear_levelling", "jsonFile": "directory-esp-idf.wear_levelling-37400b5a311656c9a22d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/wear_levelling", "targetIndexes": [84]}, {"build": "esp-idf/fatfs", "jsonFile": "directory-esp-idf.fatfs-8457c089b4d476fdffc6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/fatfs", "targetIndexes": [57]}, {"build": "esp-idf/idf_test", "jsonFile": "directory-esp-idf.idf_test-8805ce8ee6fc046f850b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/idf_test"}, {"build": "esp-idf/ieee802154", "jsonFile": "directory-esp-idf.ieee802154-216abe203d97defcb9cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/ieee802154", "targetIndexes": [62]}, {"build": "esp-idf/json", "jsonFile": "directory-esp-idf.json-ef24c3e2b91d63ccb49a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/json", "targetIndexes": [63]}, {"build": "esp-idf/mqtt", "jsonFile": "directory-esp-idf.mqtt-e9e242561df2d185fd2e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mqtt", "targetIndexes": [67]}, {"build": "esp-idf/nvs_sec_provider", "jsonFile": "directory-esp-idf.nvs_sec_provider-6417401565332ae426bc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/nvs_sec_provider", "targetIndexes": [70]}, {"build": "esp-idf/openthread", "jsonFile": "directory-esp-idf.openthread-5975f547b95c9933b518.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/openthread"}, {"build": "esp-idf/rt", "jsonFile": "directory-esp-idf.rt-87bff7f5bb2f2c123199.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/rt", "targetIndexes": [75]}, {"build": "esp-idf/spiffs", "jsonFile": "directory-esp-idf.spiffs-2c0abef6bac2c570a497.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/spiffs", "targetIndexes": [79]}, {"build": "esp-idf/ulp", "jsonFile": "directory-esp-idf.ulp-89eaa4cbc78e67867147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/ulp"}, {"build": "esp-idf/usb", "jsonFile": "directory-esp-idf.usb-362b0910e72f7241fb88.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/usb"}, {"build": "esp-idf/wifi_provisioning", "jsonFile": "directory-esp-idf.wifi_provisioning-ad885ac37cdf9ddb7c4b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/wifi_provisioning", "targetIndexes": [85]}, {"build": "esp-idf/src", "jsonFile": "directory-esp-idf.src-c23e0bc8c53af43ca0ef.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "src", "targetIndexes": [80]}], "name": "", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "esp32_environmental_monitor", "targetIndexes": [87, 88, 90, 93, 95, 104, 106, 107, 109, 110, 116, 117, 118, 125, 129, 130, 131, 132, 133]}, {"childIndexes": [2], "directoryIndexes": [1, 2, 3, 4, 5, 6, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119], "name": "esp-idf", "parentIndex": 0, "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 91, 92, 94, 96, 97, 98, 99, 100, 101, 102, 103, 105, 115, 120, 121, 122, 123, 124, 126, 127, 128]}, {"directoryIndexes": [7, 8, 9, 10, 11, 12, 13], "name": "<PERSON><PERSON>", "parentIndex": 1, "targetIndexes": [89, 108, 111, 112, 113, 114, 119]}], "targets": [{"directoryIndex": 43, "id": "__idf___pio_env::@8ed72ffb2edf9d360e6d", "jsonFile": "target-__idf___pio_env-6d9c373546f97aaf990e.json", "name": "__idf___pio_env", "projectIndex": 1}, {"directoryIndex": 47, "id": "__idf_app_trace::@dd7eb3aa23293586d3b8", "jsonFile": "target-__idf_app_trace-1232c032d8469d6a3eac.json", "name": "__idf_app_trace", "projectIndex": 1}, {"directoryIndex": 19, "id": "__idf_app_update::@cc3b426666063acc9674", "jsonFile": "target-__idf_app_update-9138cb8f9d05d41e61aa.json", "name": "__idf_app_update", "projectIndex": 1}, {"directoryIndex": 22, "id": "__idf_bootloader_support::@d88583f91142925c6676", "jsonFile": "target-__idf_bootloader_support-dffe4fd41306e666aa5e.json", "name": "__idf_bootloader_support", "projectIndex": 1}, {"directoryIndex": 64, "id": "__idf_cmock::@38f9eb59693b7394f54c", "jsonFile": "target-__idf_cmock-84e23b37b1230d6fdc20.json", "name": "__idf_cmock", "projectIndex": 1}, {"directoryIndex": 65, "id": "__idf_console::@4311ed8d271cc0ee1422", "jsonFile": "target-__idf_console-586f7d5e7696fe6f6b47.json", "name": "__idf_console", "projectIndex": 1}, {"directoryIndex": 42, "id": "__idf_cxx::@fc68b7334ac9a9392d41", "jsonFile": "target-__idf_cxx-d7ae011e7d5d891a3d36.json", "name": "__idf_cxx", "projectIndex": 1}, {"directoryIndex": 83, "id": "__idf_driver::@76403182dd99a02c203e", "jsonFile": "target-__idf_driver-cbfec8a24c44d94fd9e9.json", "name": "__idf_driver", "projectIndex": 1}, {"directoryIndex": 21, "id": "__idf_efuse::@2da764c922f8b73e9c75", "jsonFile": "target-__idf_efuse-87f84e39823011cda02b.json", "name": "__idf_efuse", "projectIndex": 1}, {"directoryIndex": 85, "id": "__idf_esp-tls::@fec40229f530785b7d3e", "jsonFile": "target-__idf_esp-tls-63484815c310da6639ad.json", "name": "__idf_esp-tls", "projectIndex": 1}, {"directoryIndex": 86, "id": "__idf_esp_adc::@847b8318c687ffb50ebb", "jsonFile": "target-__idf_esp_adc-8ede6f54675e880de72c.json", "name": "__idf_esp_adc", "projectIndex": 1}, {"directoryIndex": 17, "id": "__idf_esp_app_format::@08c2bdce7fd85cdbea06", "jsonFile": "target-__idf_esp_app_format-e6ef0e2f4301e2d795e5.json", "name": "__idf_esp_app_format", "projectIndex": 1}, {"directoryIndex": 18, "id": "__idf_esp_bootloader_format::@d2aa1b1547bed85670ee", "jsonFile": "target-__idf_esp_bootloader_format-f475ff8c78608a965de1.json", "name": "__idf_esp_bootloader_format", "projectIndex": 1}, {"directoryIndex": 58, "id": "__idf_esp_coex::@63a11468974fcf3715f9", "jsonFile": "target-__idf_esp_coex-3d3ab30c1f35a37374d5.json", "name": "__idf_esp_coex", "projectIndex": 1}, {"directoryIndex": 28, "id": "__idf_esp_common::@5ba0aac035ae9b4e20a4", "jsonFile": "target-__idf_esp_common-c88ce0cee99730cbc6b9.json", "name": "__idf_esp_common", "projectIndex": 1}, {"directoryIndex": 88, "id": "__idf_esp_driver_cam::@d005630ea6316acc7c7d", "jsonFile": "target-__idf_esp_driver_cam-e8b925cefefad8a044d3.json", "name": "__idf_esp_driver_cam", "projectIndex": 1}, {"directoryIndex": 3, "id": "__idf_esp_driver_gpio::@f5f60e7a3e445a08487c", "jsonFile": "target-__idf_esp_driver_gpio-7ed962371306a6f4bf45.json", "name": "__idf_esp_driver_gpio", "projectIndex": 1}, {"directoryIndex": 44, "id": "__idf_esp_driver_gptimer::@b8628cd9d7b32710339f", "jsonFile": "target-__idf_esp_driver_gptimer-edf3a34d826c3a8f30e8.json", "name": "__idf_esp_driver_gptimer", "projectIndex": 1}, {"directoryIndex": 79, "id": "__idf_esp_driver_i2c::@823c83e66a2d740aac64", "jsonFile": "target-__idf_esp_driver_i2c-0dd5a8e14dedfeeac18a.json", "name": "__idf_esp_driver_i2c", "projectIndex": 1}, {"directoryIndex": 69, "id": "__idf_esp_driver_i2s::@5dbaceb9cb1526e2b88d", "jsonFile": "target-__idf_esp_driver_i2s-d8afa56f16410b06e2be.json", "name": "__idf_esp_driver_i2s", "projectIndex": 1}, {"directoryIndex": 80, "id": "__idf_esp_driver_ledc::@9a01f21f38f01238ca77", "jsonFile": "target-__idf_esp_driver_ledc-1f36f165f26be1d318d3.json", "name": "__idf_esp_driver_ledc", "projectIndex": 1}, {"directoryIndex": 67, "id": "__idf_esp_driver_mcpwm::@48ac72772582b222889a", "jsonFile": "target-__idf_esp_driver_mcpwm-6b8a70555bc03f9127ab.json", "name": "__idf_esp_driver_mcpwm", "projectIndex": 1}, {"directoryIndex": 81, "id": "__idf_esp_driver_parlio::@26a02346e1abd52d0d01", "jsonFile": "target-__idf_esp_driver_parlio-d57e1a4d22966fc354e8.json", "name": "__idf_esp_driver_parlio", "projectIndex": 1}, {"directoryIndex": 66, "id": "__idf_esp_driver_pcnt::@7f5d6483ec7c43d22871", "jsonFile": "target-__idf_esp_driver_pcnt-dedee5674fd5829fba41.json", "name": "__idf_esp_driver_pcnt", "projectIndex": 1}, {"directoryIndex": 76, "id": "__idf_esp_driver_rmt::@0efb206e593213c2b54a", "jsonFile": "target-__idf_esp_driver_rmt-2e54a5e6a0bb0c20a7a1.json", "name": "__idf_esp_driver_rmt", "projectIndex": 1}, {"directoryIndex": 73, "id": "__idf_esp_driver_sdio::@c3c0139becf4e3b2a9c1", "jsonFile": "target-__idf_esp_driver_sdio-4d6a4700509869da3837.json", "name": "__idf_esp_driver_sdio", "projectIndex": 1}, {"directoryIndex": 78, "id": "__idf_esp_driver_sdm::@601b6ef28d63f823a7b4", "jsonFile": "target-__idf_esp_driver_sdm-c98e12ae6503ea9519aa.json", "name": "__idf_esp_driver_sdm", "projectIndex": 1}, {"directoryIndex": 72, "id": "__idf_esp_driver_sdspi::@c293af58c49fe790ac32", "jsonFile": "target-__idf_esp_driver_sdspi-8b66e64d8ca687421019.json", "name": "__idf_esp_driver_sdspi", "projectIndex": 1}, {"directoryIndex": 60, "id": "__idf_esp_driver_spi::@a108b58494cdd93e678c", "jsonFile": "target-__idf_esp_driver_spi-11a9e369181fe66038ac.json", "name": "__idf_esp_driver_spi", "projectIndex": 1}, {"directoryIndex": 77, "id": "__idf_esp_driver_tsens::@dad043442c4ac9e51f1a", "jsonFile": "target-__idf_esp_driver_tsens-09338cefd1a11e8d4218.json", "name": "__idf_esp_driver_tsens", "projectIndex": 1}, {"directoryIndex": 82, "id": "__idf_esp_driver_twai::@0c7b27fa84c4efa2b2b9", "jsonFile": "target-__idf_esp_driver_twai-9e1d32bc49773ee7b5c6.json", "name": "__idf_esp_driver_twai", "projectIndex": 1}, {"directoryIndex": 46, "id": "__idf_esp_driver_uart::@7494562dd0db45cfd3ba", "jsonFile": "target-__idf_esp_driver_uart-f8ebfb1ea34725b67f82.json", "name": "__idf_esp_driver_uart", "projectIndex": 1}, {"directoryIndex": 51, "id": "__idf_esp_driver_usb_serial_jtag::@5e4d6994852a8ffd473c", "jsonFile": "target-__idf_esp_driver_usb_serial_jtag-d62497a972bb316b320c.json", "name": "__idf_esp_driver_usb_serial_jtag", "projectIndex": 1}, {"directoryIndex": 93, "id": "__idf_esp_eth::@68140d310044213ed0bc", "jsonFile": "target-__idf_esp_eth-3a826dd901da06a4c214.json", "name": "__idf_esp_eth", "projectIndex": 1}, {"directoryIndex": 48, "id": "__idf_esp_event::@1426f3cbfb4523713151", "jsonFile": "target-__idf_esp_event-cca0038cea97e0063642.json", "name": "__idf_esp_event", "projectIndex": 1}, {"directoryIndex": 61, "id": "__idf_esp_gdbstub::@377a8c6ee3088554e323", "jsonFile": "target-__idf_esp_gdbstub-f144096cdd0e6a14579e.json", "name": "__idf_esp_gdbstub", "projectIndex": 1}, {"directoryIndex": 94, "id": "__idf_esp_hid::@15df40e16a5d8775dd9b", "jsonFile": "target-__idf_esp_hid-a5a3c96a312f277c6c70.json", "name": "__idf_esp_hid", "projectIndex": 1}, {"directoryIndex": 96, "id": "__idf_esp_http_client::@483ffd9ca1a06508c93f", "jsonFile": "target-__idf_esp_http_client-a3f86f39c72ae53ddd1b.json", "name": "__idf_esp_http_client", "projectIndex": 1}, {"directoryIndex": 97, "id": "__idf_esp_http_server::@1e8eede8f41363800887", "jsonFile": "target-__idf_esp_http_server-81da6181183ba2e4019a.json", "name": "__idf_esp_http_server", "projectIndex": 1}, {"directoryIndex": 98, "id": "__idf_esp_https_ota::@55ee5ec07370a5473c2b", "jsonFile": "target-__idf_esp_https_ota-870905cf0cb317e49821.json", "name": "__idf_esp_https_ota", "projectIndex": 1}, {"directoryIndex": 99, "id": "__idf_esp_https_server::@a47490a9e49b7eabdb18", "jsonFile": "target-__idf_esp_https_server-b955dcff0b2e8c9c18fa.json", "name": "__idf_esp_https_server", "projectIndex": 1}, {"directoryIndex": 35, "id": "__idf_esp_hw_support::@ce4791f5a7554c569ddb", "jsonFile": "target-__idf_esp_hw_support-77f550d5d21063b40ee7.json", "name": "__idf_esp_hw_support", "projectIndex": 1}, {"directoryIndex": 100, "id": "__idf_esp_lcd::@d5a84597e1633c9ab0d3", "jsonFile": "target-__idf_esp_lcd-0c668d745b9efd524cbd.json", "name": "__idf_esp_lcd", "projectIndex": 1}, {"directoryIndex": 103, "id": "__idf_esp_local_ctrl::@d6aad58d2946e56208ef", "jsonFile": "target-__idf_esp_local_ctrl-e6559f0f27c3fb1ed7e9.json", "name": "__idf_esp_local_ctrl", "projectIndex": 1}, {"directoryIndex": 23, "id": "__idf_esp_mm::@346d49dc35574b4eea7e", "jsonFile": "target-__idf_esp_mm-ce8bbf9e985b883061e5.json", "name": "__idf_esp_mm", "projectIndex": 1}, {"directoryIndex": 56, "id": "__idf_esp_netif::@0dd2a33d00052a11574d", "jsonFile": "target-__idf_esp_netif-54c9949964ab4872e18b.json", "name": "__idf_esp_netif", "projectIndex": 1}, {"directoryIndex": 20, "id": "__idf_esp_partition::@d7cc29bd06eede980e19", "jsonFile": "target-__idf_esp_partition-e7372d7b9e20a99e7bb4.json", "name": "__idf_esp_partition", "projectIndex": 1}, {"directoryIndex": 50, "id": "__idf_esp_phy::@c4472d8d68a6cb344214", "jsonFile": "target-__idf_esp_phy-0e939d39a11a6ce0f8e1.json", "name": "__idf_esp_phy", "projectIndex": 1}, {"directoryIndex": 5, "id": "__idf_esp_pm::@c93acdd4f72df5fa58db", "jsonFile": "target-__idf_esp_pm-9223f53c914e652ba0ee.json", "name": "__idf_esp_pm", "projectIndex": 1}, {"directoryIndex": 45, "id": "__idf_esp_ringbuf::@ad62994b1ce98b81017d", "jsonFile": "target-__idf_esp_ringbuf-d4fece44291171bea39c.json", "name": "__idf_esp_ringbuf", "projectIndex": 1}, {"directoryIndex": 29, "id": "__idf_esp_rom::@c269e361cd87ad81e927", "jsonFile": "target-__idf_esp_rom-fac90feb82d2e3d25e5f.json", "name": "__idf_esp_rom", "projectIndex": 1}, {"directoryIndex": 34, "id": "__idf_esp_security::@5b2a55663fe2034faa8b", "jsonFile": "target-__idf_esp_security-a89a9c62e22c58ed5096.json", "name": "__idf_esp_security", "projectIndex": 1}, {"directoryIndex": 25, "id": "__idf_esp_system::@874df54e61e20112dfd4", "jsonFile": "target-__idf_esp_system-a6a030b664b544af0334.json", "name": "__idf_esp_system", "projectIndex": 1}, {"directoryIndex": 4, "id": "__idf_esp_timer::@08b57a33b2000f8ac4ee", "jsonFile": "target-__idf_esp_timer-3255f58ebd0f0b9a45da.json", "name": "__idf_esp_timer", "projectIndex": 1}, {"directoryIndex": 52, "id": "__idf_esp_vfs_console::@1f6e70fca25e1b73389a", "jsonFile": "target-__idf_esp_vfs_console-2c52626e204c77395087.json", "name": "__idf_esp_vfs_console", "projectIndex": 1}, {"directoryIndex": 59, "id": "__idf_esp_wifi::@c33f1523799863800685", "jsonFile": "target-__idf_esp_wifi-4d0fa619b0ae89811ed3.json", "name": "__idf_esp_wifi", "projectIndex": 1}, {"directoryIndex": 105, "id": "__idf_espcoredump::@718b9a7d55ab42cfc0a3", "jsonFile": "target-__idf_espcoredump-235bfdc82d2b9d1cf60e.json", "name": "__idf_espcoredump", "projectIndex": 1}, {"directoryIndex": 107, "id": "__idf_fatfs::@75ac1d9b25dacc8cc9e7", "jsonFile": "target-__idf_fatfs-cd07178940d5a988c392.json", "name": "__idf_fatfs", "projectIndex": 1}, {"directoryIndex": 38, "id": "__idf_freertos::@7334568587001871092c", "jsonFile": "target-__idf_freertos-f66333aa868fdef8ac15.json", "name": "__idf_freertos", "projectIndex": 1}, {"directoryIndex": 30, "id": "__idf_hal::@41c1a00f56cd1e9ede18", "jsonFile": "target-__idf_hal-ea72d1a88fac5a20a48b.json", "name": "__idf_hal", "projectIndex": 1}, {"directoryIndex": 32, "id": "__idf_heap::@a3bf99b7c47be461ad9f", "jsonFile": "target-__idf_heap-beda5bc09748e1b2c32b.json", "name": "__idf_heap", "projectIndex": 1}, {"directoryIndex": 84, "id": "__idf_http_parser::@358cb9c2b49fd348008f", "jsonFile": "target-__idf_http_parser-b9ccff22deb34ddd3eb4.json", "name": "__idf_http_parser", "projectIndex": 1}, {"directoryIndex": 109, "id": "__idf_ieee802154::@4f032233b439602167f9", "jsonFile": "target-__idf_ieee802154-4d8adb92d2a1c1543f07.json", "name": "__idf_ieee802154", "projectIndex": 1}, {"directoryIndex": 110, "id": "__idf_json::@c78a8bdc820334f01148", "jsonFile": "target-__idf_json-669e98eb82f4cd4e8360.json", "name": "__idf_json", "projectIndex": 1}, {"directoryIndex": 31, "id": "__idf_log::@d5e1aa0ef4e3c98c8a70", "jsonFile": "target-__idf_log-2db7008a0878fbf23ea2.json", "name": "__idf_log", "projectIndex": 1}, {"directoryIndex": 54, "id": "__idf_lwip::@72bd17b7eb336521f94d", "jsonFile": "target-__idf_lwip-dced7916cab96d794b43.json", "name": "__idf_lwip", "projectIndex": 1}, {"directoryIndex": 6, "id": "__idf_mbedtls::@9d0b8d44c897d31e7973", "jsonFile": "target-__idf_mbedtls-a0820b4efe16cb142ea2.json", "name": "__idf_mbedtls", "projectIndex": 1}, {"directoryIndex": 111, "id": "__idf_mqtt::@c1ab2c6a99226d1f4b56", "jsonFile": "target-__idf_mqtt-350440b4002ed58f7282.json", "name": "__idf_mqtt", "projectIndex": 1}, {"directoryIndex": 39, "id": "__idf_newlib::@1dbc38583ffafd67f967", "jsonFile": "target-__idf_newlib-dcacfa5b5406ad593008.json", "name": "__idf_newlib", "projectIndex": 1}, {"directoryIndex": 49, "id": "__idf_nvs_flash::@b6cf96db3b320cf2c923", "jsonFile": "target-__idf_nvs_flash-a97c0705ca553e1996b7.json", "name": "__idf_nvs_flash", "projectIndex": 1}, {"directoryIndex": 112, "id": "__idf_nvs_sec_provider::@06580cba064af97f59fd", "jsonFile": "target-__idf_nvs_sec_provider-5ae6dba5524b1ea00bb0.json", "name": "__idf_nvs_sec_provider", "projectIndex": 1}, {"directoryIndex": 101, "id": "__idf_protobuf-c::@b14513c4bd859268ec22", "jsonFile": "target-__idf_protobuf-c-efa959e2025e6c9bf7cd.json", "name": "__idf_protobuf-c", "projectIndex": 1}, {"directoryIndex": 102, "id": "__idf_protocomm::@d76d01c8dec06205e1fc", "jsonFile": "target-__idf_protocomm-6dac49b073c584b9a86c.json", "name": "__idf_protocomm", "projectIndex": 1}, {"directoryIndex": 41, "id": "__idf_pthread::@52b2aa0be8fa4ca9e52e", "jsonFile": "target-__idf_pthread-f27eab77711acd7fcc53.json", "name": "__idf_pthread", "projectIndex": 1}, {"directoryIndex": 2, "id": "__idf_riscv::@5eeb1a2ca709d020776e", "jsonFile": "target-__idf_riscv-eec29740dd36351e756a.json", "name": "__idf_riscv", "projectIndex": 1}, {"directoryIndex": 114, "id": "__idf_rt::@c044be52929cb104e3c1", "jsonFile": "target-__idf_rt-e0a42ac41d8f28c0c334.json", "name": "__idf_rt", "projectIndex": 1}, {"directoryIndex": 70, "id": "__idf_sdmmc::@89ed5bca1750ffbc7db6", "jsonFile": "target-__idf_sdmmc-10cec52ad19f376819a8.json", "name": "__idf_sdmmc", "projectIndex": 1}, {"directoryIndex": 33, "id": "__idf_soc::@824f1541829f13857909", "jsonFile": "target-__idf_soc-1ec59d10f66c59b448e6.json", "name": "__idf_soc", "projectIndex": 1}, {"directoryIndex": 24, "id": "__idf_spi_flash::@6f8a8c72478269b2cd60", "jsonFile": "target-__idf_spi_flash-9e5b987d6f60af36b47b.json", "name": "__idf_spi_flash", "projectIndex": 1}, {"directoryIndex": 115, "id": "__idf_spiffs::@df881b71474064c86689", "jsonFile": "target-__idf_spiffs-46ffbc69d01409181385.json", "name": "__idf_spiffs", "projectIndex": 1}, {"directoryIndex": 119, "id": "__idf_src::@2478b13de56d3028f688", "jsonFile": "target-__idf_src-6cc885b783712583c0d5.json", "name": "__idf_src", "projectIndex": 1}, {"directoryIndex": 95, "id": "__idf_tcp_transport::@2217ed266b3ca9834900", "jsonFile": "target-__idf_tcp_transport-512cf2ded359aed0f3f0.json", "name": "__idf_tcp_transport", "projectIndex": 1}, {"directoryIndex": 63, "id": "__idf_unity::@fec746937506b5690992", "jsonFile": "target-__idf_unity-4f085601103f32074c37.json", "name": "__idf_unity", "projectIndex": 1}, {"directoryIndex": 53, "id": "__idf_vfs::@57481f90220bce4f7e1a", "jsonFile": "target-__idf_vfs-7269f804c5debc51895c.json", "name": "__idf_vfs", "projectIndex": 1}, {"directoryIndex": 106, "id": "__idf_wear_levelling::@cb21b0d7c0815cff11c6", "jsonFile": "target-__idf_wear_levelling-8d9f358e08296970bede.json", "name": "__idf_wear_levelling", "projectIndex": 1}, {"directoryIndex": 118, "id": "__idf_wifi_provisioning::@9d7e7ffe1b15746c1098", "jsonFile": "target-__idf_wifi_provisioning-8ce1a624b526d35c48ea.json", "name": "__idf_wifi_provisioning", "projectIndex": 1}, {"directoryIndex": 57, "id": "__idf_wpa_supplicant::@2124e86f93fbab1c8043", "jsonFile": "target-__idf_wpa_supplicant-062ddb1d5403e483a4d8.json", "name": "__idf_wpa_supplicant", "projectIndex": 1}, {"directoryIndex": 0, "id": "__ldgen_output_sections.ld::@6890427a1f51a3e7e1df", "jsonFile": "target-__ldgen_output_sections.ld-7ddcd37a531e91e7ac79.json", "name": "__ldgen_output_sections.ld", "projectIndex": 0}, {"directoryIndex": 0, "id": "_project_elf_src::@6890427a1f51a3e7e1df", "jsonFile": "target-_project_elf_src-0ac750310e3de62d3c06.json", "name": "_project_elf_src", "projectIndex": 0}, {"directoryIndex": 7, "id": "apidoc::@b8f87a1a94d8d9bb59bb", "jsonFile": "target-apidoc-bbe932024f4756ce6461.json", "name": "apidoc", "projectIndex": 2}, {"directoryIndex": 0, "id": "app::@6890427a1f51a3e7e1df", "jsonFile": "target-app-b84a2e58d0f73eaca131.json", "name": "app", "projectIndex": 0}, {"directoryIndex": 15, "id": "app-flash::@bf8f55be585a12323665", "jsonFile": "target-app-flash-2c51074c33950916f493.json", "name": "app-flash", "projectIndex": 1}, {"directoryIndex": 15, "id": "app_check_size::@bf8f55be585a12323665", "jsonFile": "target-app_check_size-3aaf705a272f34d9d8b2.json", "name": "app_check_size", "projectIndex": 1}, {"directoryIndex": 0, "id": "bootloader::@6890427a1f51a3e7e1df", "jsonFile": "target-bootloader-3545b5edd9d53878b7b4.json", "name": "bootloader", "projectIndex": 0}, {"directoryIndex": 14, "id": "bootloader-flash::@4013c5adf9c02e1a402c", "jsonFile": "target-bootloader-flash-5f212f902284520eaaeb.json", "name": "bootloader-flash", "projectIndex": 1}, {"directoryIndex": 0, "id": "confserver::@6890427a1f51a3e7e1df", "jsonFile": "target-confserver-ae42845981f256c8bb2a.json", "name": "confserver", "projectIndex": 0}, {"directoryIndex": 6, "id": "custom_bundle::@9d0b8d44c897d31e7973", "jsonFile": "target-custom_bundle-4b366a297b2f1b0c824b.json", "name": "custom_bundle", "projectIndex": 1}, {"directoryIndex": 21, "id": "efuse-common-table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse-common-table-17d171ec94f1ecea8c56.json", "name": "efuse-common-table", "projectIndex": 1}, {"directoryIndex": 21, "id": "efuse-custom-table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse-custom-table-ea03126234e6cc25d23c.json", "name": "efuse-custom-table", "projectIndex": 1}, {"directoryIndex": 21, "id": "efuse_common_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_common_table-435ab54804d32d204831.json", "name": "efuse_common_table", "projectIndex": 1}, {"directoryIndex": 21, "id": "efuse_custom_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_custom_table-8e8919d697cf29e2ba6c.json", "name": "efuse_custom_table", "projectIndex": 1}, {"directoryIndex": 21, "id": "efuse_test_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_test_table-8a4df152a35132d32c8d.json", "name": "efuse_test_table", "projectIndex": 1}, {"directoryIndex": 15, "id": "encrypted-app-flash::@bf8f55be585a12323665", "jsonFile": "target-encrypted-app-flash-1c64781e06d6b9007e43.json", "name": "encrypted-app-flash", "projectIndex": 1}, {"directoryIndex": 14, "id": "encrypted-bootloader-flash::@4013c5adf9c02e1a402c", "jsonFile": "target-encrypted-bootloader-flash-6dbbf0dfa34c8c0c1c7f.json", "name": "encrypted-bootloader-flash", "projectIndex": 1}, {"directoryIndex": 0, "id": "encrypted-flash::@6890427a1f51a3e7e1df", "jsonFile": "target-encrypted-flash-e7a77717de4bab4a2f90.json", "name": "encrypted-flash", "projectIndex": 0}, {"directoryIndex": 16, "id": "encrypted-partition-table-flash::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-encrypted-partition-table-flash-f4e4937cdbeeb0d47536.json", "name": "encrypted-partition-table-flash", "projectIndex": 1}, {"directoryIndex": 0, "id": "erase_flash::@6890427a1f51a3e7e1df", "jsonFile": "target-erase_flash-2aee3ffabe41dadb12f7.json", "name": "erase_flash", "projectIndex": 0}, {"directoryIndex": 0, "id": "esp32_environmental_monitor.elf::@6890427a1f51a3e7e1df", "jsonFile": "target-esp32_environmental_monitor.elf-33aa083a3821517d9545.json", "name": "esp32_environmental_monitor.elf", "projectIndex": 0}, {"directoryIndex": 10, "id": "everest::@3ecd26ac3776729513f0", "jsonFile": "target-everest-8ebb7d4c18ad3a87b9cb.json", "name": "everest", "projectIndex": 2}, {"directoryIndex": 0, "id": "flash::@6890427a1f51a3e7e1df", "jsonFile": "target-flash-5a5bb4e3fa820d356dc7.json", "name": "flash", "projectIndex": 0}, {"directoryIndex": 0, "id": "gen_project_binary::@6890427a1f51a3e7e1df", "jsonFile": "target-gen_project_binary-5eefa2ba631bc7364ea0.json", "name": "gen_project_binary", "projectIndex": 0}, {"directoryIndex": 12, "id": "lib::@12759e2a35724d846b97", "jsonFile": "target-lib-9af13863b9615cb2566a.json", "name": "lib", "projectIndex": 2}, {"directoryIndex": 12, "id": "mbedcrypto::@12759e2a35724d846b97", "jsonFile": "target-mbedcrypto-fed7fcade65a16877b90.json", "name": "mbedcrypto", "projectIndex": 2}, {"directoryIndex": 12, "id": "mbedtls::@12759e2a35724d846b97", "jsonFile": "target-mbedtls-69c67ffd0fc8b278abbc.json", "name": "mbedtls", "projectIndex": 2}, {"directoryIndex": 12, "id": "mbedx509::@12759e2a35724d846b97", "jsonFile": "target-mbedx509-a435d7bc35b581f89d5f.json", "name": "mbedx509", "projectIndex": 2}, {"directoryIndex": 25, "id": "memory.ld::@874df54e61e20112dfd4", "jsonFile": "target-memory.ld-621d2bc67f78a87a508b.json", "name": "memory.ld", "projectIndex": 1}, {"directoryIndex": 0, "id": "menuconfig::@6890427a1f51a3e7e1df", "jsonFile": "target-menuconfig-42a874d6bc5663ce6b21.json", "name": "menuconfig", "projectIndex": 0}, {"directoryIndex": 0, "id": "merge-bin::@6890427a1f51a3e7e1df", "jsonFile": "target-merge-bin-09a2c980c840bd6d22b5.json", "name": "merge-bin", "projectIndex": 0}, {"directoryIndex": 0, "id": "monitor::@6890427a1f51a3e7e1df", "jsonFile": "target-monitor-420082cf5b84c0d89f9b.json", "name": "monitor", "projectIndex": 0}, {"directoryIndex": 11, "id": "p256m::@c854ae7c6350d6f63df0", "jsonFile": "target-p256m-90789ad6293648ccfa84.json", "name": "p256m", "projectIndex": 2}, {"directoryIndex": 16, "id": "partition-table::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition-table-dce7915a9b09edaf4336.json", "name": "partition-table", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition-table-flash::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition-table-flash-16c2057885d815494d2e.json", "name": "partition-table-flash", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition_table::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition_table-a01c4693d2c875f995e1.json", "name": "partition_table", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition_table-flash::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition_table-flash-c6e419ad8a61683d2a0d.json", "name": "partition_table-flash", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition_table_bin::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition_table_bin-b4cda051ed3d6d8fddb7.json", "name": "partition_table_bin", "projectIndex": 1}, {"directoryIndex": 0, "id": "save-defconfig::@6890427a1f51a3e7e1df", "jsonFile": "target-save-defconfig-d496363efcf79908c2a0.json", "name": "save-defconfig", "projectIndex": 0}, {"directoryIndex": 25, "id": "sections.ld.in::@874df54e61e20112dfd4", "jsonFile": "target-sections.ld.in-7e9cce01d28ddfe767dd.json", "name": "sections.ld.in", "projectIndex": 1}, {"directoryIndex": 21, "id": "show-efuse-table::@2da764c922f8b73e9c75", "jsonFile": "target-show-efuse-table-21b8b9717d274752d79a.json", "name": "show-efuse-table", "projectIndex": 1}, {"directoryIndex": 21, "id": "show_efuse_table::@2da764c922f8b73e9c75", "jsonFile": "target-show_efuse_table-8bfb09f7abc2c3307064.json", "name": "show_efuse_table", "projectIndex": 1}, {"directoryIndex": 0, "id": "size::@6890427a1f51a3e7e1df", "jsonFile": "target-size-502b9de6fdfa4b4b19fb.json", "name": "size", "projectIndex": 0}, {"directoryIndex": 0, "id": "size-components::@6890427a1f51a3e7e1df", "jsonFile": "target-size-components-222c220bdae35bd0f95d.json", "name": "size-components", "projectIndex": 0}, {"directoryIndex": 0, "id": "size-files::@6890427a1f51a3e7e1df", "jsonFile": "target-size-files-958f92e848828b8eb4ea.json", "name": "size-files", "projectIndex": 0}, {"directoryIndex": 0, "id": "uf2::@6890427a1f51a3e7e1df", "jsonFile": "target-uf2-ff651f27eab4f8c6e428.json", "name": "uf2", "projectIndex": 0}, {"directoryIndex": 0, "id": "uf2-app::@6890427a1f51a3e7e1df", "jsonFile": "target-uf2-app-57446b9fe6590c42ec64.json", "name": "uf2-app", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1", "source": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver"}, "version": {"major": 2, "minor": 7}}