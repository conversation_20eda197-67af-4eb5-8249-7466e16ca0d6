{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "__ldgen_create_target", "idf_build_executable", "project"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/ldgen.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/build.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 6, "parent": 0}, {"command": 2, "file": 2, "line": 972, "parent": 1}, {"command": 1, "file": 1, "line": 734, "parent": 2}, {"command": 0, "file": 0, "line": 200, "parent": 3}]}, "dependencies": [{"id": "__idf___pio_env::@8ed72ffb2edf9d360e6d"}, {"id": "__idf_app_trace::@dd7eb3aa23293586d3b8"}, {"id": "__idf_unity::@fec746937506b5690992"}, {"id": "__idf_cmock::@38f9eb59693b7394f54c"}, {"id": "__idf_console::@4311ed8d271cc0ee1422"}, {"id": "__idf_esp_driver_cam::@d005630ea6316acc7c7d"}, {"id": "__idf_esp_eth::@68140d310044213ed0bc"}, {"id": "__idf_esp_hid::@15df40e16a5d8775dd9b"}, {"id": "__idf_esp_https_server::@a47490a9e49b7eabdb18"}, {"id": "__idf_riscv::@5eeb1a2ca709d020776e"}, {"id": "__idf_src::@2478b13de56d3028f688"}, {"id": "__idf_wifi_provisioning::@9d7e7ffe1b15746c1098"}, {"id": "__idf_protobuf-c::@b14513c4bd859268ec22"}, {"id": "__idf_esp_lcd::@d5a84597e1633c9ab0d3"}, {"id": "__idf_protocomm::@d76d01c8dec06205e1fc"}, {"id": "__idf_esp_local_ctrl::@d6aad58d2946e56208ef"}, {"id": "__idf_espcoredump::@718b9a7d55ab42cfc0a3"}, {"id": "__idf_wear_levelling::@cb21b0d7c0815cff11c6"}, {"id": "__idf_fatfs::@75ac1d9b25dacc8cc9e7"}, {"id": "__idf_ieee802154::@4f032233b439602167f9"}, {"id": "__idf_json::@c78a8bdc820334f01148"}, {"id": "__idf_mqtt::@c1ab2c6a99226d1f4b56"}, {"id": "__idf_nvs_sec_provider::@06580cba064af97f59fd"}, {"id": "__idf_rt::@c044be52929cb104e3c1"}, {"id": "__idf_spiffs::@df881b71474064c86689"}], "id": "__ldgen_output_sections.ld::@6890427a1f51a3e7e1df", "name": "__ldgen_output_sections.ld", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": ".pio/build/esp32-c6-devkitc-1/CMakeFiles/__ldgen_output_sections.ld", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": ".pio/build/esp32-c6-devkitc-1/CMakeFiles/__ldgen_output_sections.ld.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": ".pio/build/esp32-c6-devkitc-1/esp-idf/esp_system/ld/sections.ld.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}