// Chart.js configurations and implementations
class ChartManager {
    constructor() {
        this.temperatureChart = null;
        this.humidityChart = null;
        this.recentDataChart = null;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeCharts());
        } else {
            this.initializeCharts();
        }
    }

    initializeCharts() {
        this.createTemperatureGauge();
        this.createHumidityGauge();
        this.createRecentDataChart();
    }

    createTemperatureGauge() {
        const ctx = document.getElementById('temperature-gauge');
        if (!ctx) return;

        this.temperatureChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [0, 100],
                    backgroundColor: [
                        'rgba(37, 99, 235, 0.8)',
                        'rgba(226, 232, 240, 0.3)'
                    ],
                    borderWidth: 0,
                    cutout: '75%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        });

        // Add update method
        this.temperatureChart.updateGauge = (value) => {
            const percentage = Math.min(Math.max((value + 10) / 60 * 100, 0), 100);
            this.temperatureChart.data.datasets[0].data = [percentage, 100 - percentage];
            
            // Update colors based on temperature
            const color = this.getTemperatureColor(value);
            this.temperatureChart.data.datasets[0].backgroundColor[0] = color;
            this.temperatureChart.update('none');
        };
    }

    createHumidityGauge() {
        const ctx = document.getElementById('humidity-gauge');
        if (!ctx) return;

        this.humidityChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [0, 100],
                    backgroundColor: [
                        'rgba(6, 182, 212, 0.8)',
                        'rgba(226, 232, 240, 0.3)'
                    ],
                    borderWidth: 0,
                    cutout: '75%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        });

        // Add update method
        this.humidityChart.updateGauge = (value) => {
            const percentage = Math.min(Math.max(value, 0), 100);
            this.humidityChart.data.datasets[0].data = [percentage, 100 - percentage];
            
            // Update colors based on humidity
            const color = this.getHumidityColor(value);
            this.humidityChart.data.datasets[0].backgroundColor[0] = color;
            this.humidityChart.update('none');
        };
    }

    createRecentDataChart() {
        const ctx = document.getElementById('recent-data-chart');
        if (!ctx) return;

        this.recentDataChart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: 'Temperature (°C)',
                        data: [],
                        borderColor: 'rgba(37, 99, 235, 1)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Humidity (%)',
                        data: [],
                        borderColor: 'rgba(6, 182, 212, 1)',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y1'
                    },
                    {
                        label: 'Air Quality',
                        data: [],
                        borderColor: 'rgba(16, 185, 129, 1)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y2'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                hour: 'HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Temperature (°C)'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Humidity (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        max: 100,
                        min: 0
                    },
                    y2: {
                        type: 'linear',
                        display: false,
                        max: 100,
                        min: 0
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // Add update methods
        this.recentDataChart.updateTimeframe = async (timeframe) => {
            try {
                const response = await fetch(`/api/history?timeframe=${timeframe}`);
                if (response.ok) {
                    const data = await response.json();
                    this.updateChartData(data);
                }
            } catch (error) {
                console.error('Error updating chart data:', error);
            }
        };

        // Load initial data
        this.loadInitialChartData();
    }

    async loadInitialChartData() {
        // Generate sample data for demonstration
        const now = new Date();
        const sampleData = [];
        
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - (i * 60 * 60 * 1000));
            sampleData.push({
                time: time,
                temperature: 20 + Math.sin(i * 0.5) * 5 + Math.random() * 2,
                humidity: 50 + Math.cos(i * 0.3) * 20 + Math.random() * 5,
                airQuality: 30 + Math.sin(i * 0.2) * 15 + Math.random() * 10
            });
        }

        this.updateChartData({ data: sampleData });
    }

    updateChartData(chartData) {
        if (!this.recentDataChart || !chartData.data) return;

        const temperatureData = [];
        const humidityData = [];
        const airQualityData = [];

        chartData.data.forEach(point => {
            const time = new Date(point.time);
            temperatureData.push({ x: time, y: point.temperature });
            humidityData.push({ x: time, y: point.humidity });
            airQualityData.push({ x: time, y: point.airQuality });
        });

        this.recentDataChart.data.datasets[0].data = temperatureData;
        this.recentDataChart.data.datasets[1].data = humidityData;
        this.recentDataChart.data.datasets[2].data = airQualityData;

        this.recentDataChart.update();
    }

    getTemperatureColor(temperature) {
        if (temperature < 0) return 'rgba(59, 130, 246, 0.8)'; // Blue - very cold
        if (temperature < 10) return 'rgba(6, 182, 212, 0.8)'; // Cyan - cold
        if (temperature < 20) return 'rgba(16, 185, 129, 0.8)'; // Green - cool
        if (temperature < 25) return 'rgba(34, 197, 94, 0.8)'; // Light green - comfortable
        if (temperature < 30) return 'rgba(245, 158, 11, 0.8)'; // Yellow - warm
        if (temperature < 35) return 'rgba(249, 115, 22, 0.8)'; // Orange - hot
        return 'rgba(239, 68, 68, 0.8)'; // Red - very hot
    }

    getHumidityColor(humidity) {
        if (humidity < 30) return 'rgba(239, 68, 68, 0.8)'; // Red - too dry
        if (humidity < 40) return 'rgba(245, 158, 11, 0.8)'; // Yellow - dry
        if (humidity < 60) return 'rgba(16, 185, 129, 0.8)'; // Green - comfortable
        if (humidity < 70) return 'rgba(6, 182, 212, 0.8)'; // Cyan - humid
        return 'rgba(59, 130, 246, 0.8)'; // Blue - very humid
    }
}

// Initialize charts when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.chartManager = new ChartManager();
    
    // Make charts available globally
    window.temperatureChart = window.chartManager.temperatureChart;
    window.humidityChart = window.chartManager.humidityChart;
    window.recentDataChart = window.chartManager.recentDataChart;
});
