
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:200 (message)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      The target system is: Generic -  - 
      The host system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/riscv32-esp-elf-gcc.exe 
      Build flags: -march=rv32imac_zicsr_zifencei
      Id flags:  
      
      The output was:
      0
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-closer.o): in function `_close_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/closer.c:47:(.text+0x14): warning: _close is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-lseekr.o): in function `_lseek_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/lseekr.c:49:(.text+0x18): warning: _lseek is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-readr.o): in function `_read_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/readr.c:49:(.text+0x18): warning: _read is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-writer.o): in function `_write_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/writer.c:49:(.text+0x18): warning: _write is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-fclose.o): in function `fclose':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/stdio/fclose.c:125:(.text+0xf4): warning: __getreent is not implemented and will always fail
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/3.30.2/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/riscv32-esp-elf-g++.exe 
      Build flags: -march=rv32imac_zicsr_zifencei
      Id flags:  
      
      The output was:
      0
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-closer.o): in function `_close_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/closer.c:47:(.text+0x14): warning: _close is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-lseekr.o): in function `_lseek_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/lseekr.c:49:(.text+0x18): warning: _lseek is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-readr.o): in function `_read_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/readr.c:49:(.text+0x18): warning: _read is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-writer.o): in function `_write_r':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/reent/writer.c:49:(.text+0x18): warning: _write is not implemented and will always fail
      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32\\libc.a(libc_a-fclose.o): in function `fclose':
      /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/newlib/newlib/libc/stdio/fclose.c:125:(.text+0xf4): warning: __getreent is not implemented and will always fail
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/3.30.2/CompilerIdCXX/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1192 (message)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      riscv32-esp-elf-gcc.exe (crosstool-NG esp-14.2.0_20241119) 14.2.0
      Copyright (C) 2024 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-g19w86"
      binary: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-g19w86"
    cmakeVariables:
      CMAKE_C_FLAGS: "-march=rv32imac_zicsr_zifencei "
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-nostartfiles -march=rv32imac_zicsr_zifencei "
      CMAKE_MODULE_PATH: "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake;C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/third_party"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-g19w86'
        
        Run Build Command(s): C:/Users/<USER>/.platformio/packages/tool-ninja/ninja.exe -v cmTC_15446
        [1/2] C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe   -march=rv32imac_zicsr_zifencei     -v -o CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj -c C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe
        Target: riscv32-esp-elf
        Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) 
        COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_15446.dir/'
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/cc1.exe -quiet -v -imultilib rv32imac_zicsr_zifencei/ilp32 -iprefix C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/ -isysroot C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_15446.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -march=rv32imac_zicsr_zifencei -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5R0iec.s
        GNU C17 (crosstool-NG esp-14.2.0_20241119) version 14.2.0 (riscv32-esp-elf)
        	compiled by GNU C version 6.3.0 20170516, GMP version 6.2.1, MPFR version 4.2.1, MPC version 1.2.1, isl version isl-0.26-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include"
        ignoring nonexistent directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/builds/idf/crosstool-NG/builds/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/14.2.0/../../../../include"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include
        End of search list.
        Compiler executable checksum: bd4b44cd937a52babfee906ad526682a
        COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_15446.dir/'
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/as.exe -v --traditional-format -march=rv32imac_zicsr_zifencei -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -o CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5R0iec.s
        GNU assembler version 2.43.1 (riscv32-esp-elf) using BFD version (crosstool-NG esp-14.2.0_20241119) 2.43.1
        COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/
        LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/
        COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.'
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe -march=rv32imac_zicsr_zifencei -nostartfiles -march=rv32imac_zicsr_zifencei  -v CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj -o cmTC_15446   && cd ."
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe
        Target: riscv32-esp-elf
        Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) 
        COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/
        LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/
        COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_15446' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_15446.'
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/collect2.exe -plugin C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccrlv43e.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc --sysroot=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf -melf32lriscv -X -o cmTC_15446 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj -lgcc -lc -lnosys -lc -lgcc
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: warning: cannot find entry symbol _start; defaulting to 00010094
        COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_15446' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_15446.'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include]
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed]
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include]
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include-fixed]
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include]
        implicit include dirs: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include-fixed;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-g19w86']
        ignore line: []
        ignore line: [Run Build Command(s): C:/Users/<USER>/.platformio/packages/tool-ninja/ninja.exe -v cmTC_15446]
        ignore line: [[1/2] C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe   -march=rv32imac_zicsr_zifencei     -v -o CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj -c C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe]
        ignore line: [Target: riscv32-esp-elf]
        ignore line: [Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) ]
        ignore line: [COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_15446.dir/']
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/cc1.exe -quiet -v -imultilib rv32imac_zicsr_zifencei/ilp32 -iprefix C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/ -isysroot C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_15446.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -march=rv32imac_zicsr_zifencei -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5R0iec.s]
        ignore line: [GNU C17 (crosstool-NG esp-14.2.0_20241119) version 14.2.0 (riscv32-esp-elf)]
        ignore line: [	compiled by GNU C version 6.3.0 20170516  GMP version 6.2.1  MPFR version 4.2.1  MPC version 1.2.1  isl version isl-0.26-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include"]
        ignore line: [ignoring nonexistent directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/builds/idf/crosstool-NG/builds/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/14.2.0/../../../../include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: bd4b44cd937a52babfee906ad526682a]
        ignore line: [COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_15446.dir/']
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/as.exe -v --traditional-format -march=rv32imac_zicsr_zifencei -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -o CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5R0iec.s]
        ignore line: [GNU assembler version 2.43.1 (riscv32-esp-elf) using BFD version (crosstool-NG esp-14.2.0_20241119) 2.43.1]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.']
        ignore line: [[2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe -march=rv32imac_zicsr_zifencei -nostartfiles -march=rv32imac_zicsr_zifencei  -v CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj -o cmTC_15446   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe]
        ignore line: [Target: riscv32-esp-elf]
        ignore line: [Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_15446' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_15446.']
        link line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/collect2.exe -plugin C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccrlv43e.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc --sysroot=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf -melf32lriscv -X -o cmTC_15446 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj -lgcc -lc -lnosys -lc -lgcc]
          arg [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccrlv43e.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lnosys] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--sysroot=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf] ==> ignore
          arg [-melf32lriscv] ==> ignore
          arg [-X] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_15446] ==> ignore
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib]
          arg [CMakeFiles/cmTC_15446.dir/CMakeCCompilerABI.c.obj] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lnosys] ==> lib [nosys]
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: warning: cannot find entry symbol _start]
        ignore line: [ defaulting to 00010094]
        ignore line: [COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_15446' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_15446.']
        ignore line: []
        ignore line: []
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib]
        implicit libs: [gcc;c;nosys;c;gcc]
        implicit objs: []
        implicit dirs: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-nfe30q"
      binary: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-nfe30q"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-march=rv32imac_zicsr_zifencei "
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "-nostartfiles -march=rv32imac_zicsr_zifencei "
      CMAKE_MODULE_PATH: "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake;C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/third_party"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-nfe30q'
        
        Run Build Command(s): C:/Users/<USER>/.platformio/packages/tool-ninja/ninja.exe -v cmTC_f8d9c
        [1/2] C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe   -march=rv32imac_zicsr_zifencei     -v -o CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj -c C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe
        Target: riscv32-esp-elf
        Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) 
        COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_f8d9c.dir/'
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/cc1plus.exe -quiet -v -imultilib rv32imac_zicsr_zifencei/ilp32 -iprefix C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/ -isysroot C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_f8d9c.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -march=rv32imac_zicsr_zifencei -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZ2xDuw.s
        GNU C++17 (crosstool-NG esp-14.2.0_20241119) version 14.2.0 (riscv32-esp-elf)
        	compiled by GNU C version 6.3.0 20170516, GMP version 6.2.1, MPFR version 4.2.1, MPC version 1.2.1, isl version isl-0.26-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/backward"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include"
        ignoring nonexistent directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/builds/idf/crosstool-NG/builds/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/14.2.0/../../../../include"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include"
        ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/backward
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include
        End of search list.
        Compiler executable checksum: b8daf4944b33b24ed9ecf5a047df93fe
        COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_f8d9c.dir/'
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/as.exe -v --traditional-format -march=rv32imac_zicsr_zifencei -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -o CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZ2xDuw.s
        GNU assembler version 2.43.1 (riscv32-esp-elf) using BFD version (crosstool-NG esp-14.2.0_20241119) 2.43.1
        COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/
        LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/
        COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.'
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe -march=rv32imac_zicsr_zifencei -nostartfiles -march=rv32imac_zicsr_zifencei  -v CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_f8d9c   && cd ."
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe
        Target: riscv32-esp-elf
        Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) 
        COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/
        LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/
        COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_f8d9c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_f8d9c.'
         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/collect2.exe -plugin C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc1zgceA.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc --sysroot=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf -melf32lriscv -X -o cmTC_f8d9c -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj -lstdc++ -lm -lgcc -lc -lnosys -lc -lgcc
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: warning: cannot find entry symbol _start; defaulting to 00010094
        COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_f8d9c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_f8d9c.'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0]
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32]
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/backward]
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include]
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed]
          add: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include/c++/14.2.0]
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32]
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/backward] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include/c++/14.2.0/backward]
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include]
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include-fixed]
        collapse include dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include]
        implicit include dirs: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include/c++/14.2.0;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include/c++/14.2.0/backward;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/include-fixed;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake:589 (__project)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-nfe30q']
        ignore line: []
        ignore line: [Run Build Command(s): C:/Users/<USER>/.platformio/packages/tool-ninja/ninja.exe -v cmTC_f8d9c]
        ignore line: [[1/2] C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe   -march=rv32imac_zicsr_zifencei     -v -o CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj -c C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe]
        ignore line: [Target: riscv32-esp-elf]
        ignore line: [Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) ]
        ignore line: [COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_f8d9c.dir/']
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/cc1plus.exe -quiet -v -imultilib rv32imac_zicsr_zifencei/ilp32 -iprefix C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/ -isysroot C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_f8d9c.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -march=rv32imac_zicsr_zifencei -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZ2xDuw.s]
        ignore line: [GNU C++17 (crosstool-NG esp-14.2.0_20241119) version 14.2.0 (riscv32-esp-elf)]
        ignore line: [	compiled by GNU C version 6.3.0 20170516  GMP version 6.2.1  MPFR version 4.2.1  MPC version 1.2.1  isl version isl-0.26-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/backward"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include"]
        ignore line: [ignoring nonexistent directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/builds/idf/crosstool-NG/builds/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/14.2.0/../../../../include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/../../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/riscv32-esp-elf/rv32imac_zicsr_zifencei/ilp32]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include/c++/14.2.0/backward]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/include-fixed]
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: b8daf4944b33b24ed9ecf5a047df93fe]
        ignore line: [COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_f8d9c.dir/']
        ignore line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/as.exe -v --traditional-format -march=rv32imac_zicsr_zifencei -march=rv32imac_zicsr_zifencei -mabi=ilp32 -misa-spec=20191213 -o CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZ2xDuw.s]
        ignore line: [GNU assembler version 2.43.1 (riscv32-esp-elf) using BFD version (crosstool-NG esp-14.2.0_20241119) 2.43.1]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-march=rv32imac_zicsr_zifencei' '-v' '-o' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [[2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe -march=rv32imac_zicsr_zifencei -nostartfiles -march=rv32imac_zicsr_zifencei  -v CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_f8d9c   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe]
        ignore line: [Target: riscv32-esp-elf]
        ignore line: [Configured with: /builds/idf/crosstool-NG/.build/riscv32-esp-elf/src/gcc/configure --build=x86_64-build_pc-linux-gnu --host=x86_64-host_w64-mingw32 --target=riscv32-esp-elf --prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --exec_prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf --with-local-prefix=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-sysroot=/builds/idf/crosstool-NG/builds/riscv32-esp-elf/riscv32-esp-elf --with-native-system-header-dir=/include --with-newlib --enable-threads=no --disable-shared --with-arch=rv32gc --with-abi=ilp32 --with-pkgversion='crosstool-NG esp-14.2.0_20241119' --disable-__cxa_atexit --enable-cxx-flags=-ffunction-sections --disable-libgomp --disable-libmudflap --disable-libmpx --disable-libssp --disable-libquadmath --disable-libquadmath-support --disable-libstdcxx-verbose --with-gmp=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpfr=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-mpc=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --with-isl=/builds/idf/crosstool-NG/.build/riscv32-esp-elf/buildtools/complibs-host --enable-lto --enable-target-optspace --without-long-double-128 --disable-nls --enable-multiarch --enable-languages=c,c++ --disable-libstdcxx-verbose --enable-threads=posix --enable-gcov-custom-rtio --enable-libstdcxx-time=yes --with-gnu-ld]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (crosstool-NG esp-14.2.0_20241119) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_f8d9c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_f8d9c.']
        link line: [ C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/collect2.exe -plugin C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc1zgceA.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc --sysroot=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf -melf32lriscv -X -o cmTC_f8d9c -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0 -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib -LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj -lstdc++ -lm -lgcc -lc -lnosys -lc -lgcc]
          arg [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../libexec/gcc/riscv32-esp-elf/14.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc1zgceA.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lnosys] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--sysroot=C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf] ==> ignore
          arg [-melf32lriscv] ==> ignore
          arg [-X] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_f8d9c] ==> ignore
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib]
          arg [-LC:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib] ==> dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib]
          arg [CMakeFiles/cmTC_f8d9c.dir/CMakeCXXCompilerABI.cpp.obj] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lnosys] ==> lib [nosys]
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
        ignore line: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: warning: cannot find entry symbol _start]
        ignore line: [ defaulting to 00010094]
        ignore line: [COLLECT_GCC_OPTIONS='-nostartfiles' '-march=rv32imac_zicsr_zifencei' '-v' '-o' 'cmTC_f8d9c' '-mabi=ilp32' '-misa-spec=20191213' '-march=rv32imac_zicsr_zifencei' '-dumpdir' 'cmTC_f8d9c.']
        ignore line: []
        ignore line: []
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib]
        collapse library dir [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../riscv32-esp-elf/lib] ==> [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib]
        implicit libs: [stdc++;m;gcc;c;nosys;c;gcc]
        implicit objs: []
        implicit dirs: [C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc/riscv32-esp-elf/14.2.0;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/lib/gcc;C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/CMakeLists.txt:139 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-kny884"
      binary: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-kny884"
    cmakeVariables:
      CMAKE_C_FLAGS: "-march=rv32imac_zicsr_zifencei "
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-nostartfiles -march=rv32imac_zicsr_zifencei "
      CMAKE_MODULE_PATH: "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake;C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/third_party"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-kny884'
        
        Run Build Command(s): C:/Users/<USER>/.platformio/packages/tool-ninja/ninja.exe -v cmTC_2b8c4
        [1/2] C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe -DCMAKE_HAVE_LIBC_PTHREAD  -march=rv32imac_zicsr_zifencei -o CMakeFiles/cmTC_2b8c4.dir/src.c.obj -c C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-kny884/src.c
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe -march=rv32imac_zicsr_zifencei -nostartfiles -march=rv32imac_zicsr_zifencei CMakeFiles/cmTC_2b8c4.dir/src.c.obj -o cmTC_2b8c4   && cd ."
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: CMakeFiles/cmTC_2b8c4.dir/src.c.obj: in function `main':
        src.c:(.text+0x6e): warning: pthread_atfork is not implemented and will always fail
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: src.c:(.text+0x50): warning: pthread_cancel is not implemented and will always fail
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: src.c:(.text+0x34): warning: pthread_create is not implemented and will always fail
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: src.c:(.text+0x42): warning: pthread_detach is not implemented and will always fail
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: src.c:(.text+0x78): warning: pthread_exit is not implemented and will always fail
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: src.c:(.text+0x60): warning: pthread_join is not implemented and will always fail
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: warning: cannot find entry symbol _start; defaulting to 00010094
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30/Modules/CheckCCompilerFlag.cmake:51 (cmake_check_compiler_flag)"
      - "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/CMakeLists.txt:222 (CHECK_C_COMPILER_FLAG)"
    checks:
      - "Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS"
    directories:
      source: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-ozgsbe"
      binary: "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-ozgsbe"
    cmakeVariables:
      CMAKE_C_FLAGS: "-march=rv32imac_zicsr_zifencei  -Wall -Wextra -Wwrite-strings -Wmissing-prototypes -Wformat=2 -Wno-format-nonliteral -Wvla -Wlogical-op -Wshadow"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-nostartfiles -march=rv32imac_zicsr_zifencei "
      CMAKE_MODULE_PATH: "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake;C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/third_party"
    buildResult:
      variable: "C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-ozgsbe'
        
        Run Build Command(s): C:/Users/<USER>/.platformio/packages/tool-ninja/ninja.exe -v cmTC_94877
        [1/2] C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe -DC_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS  -march=rv32imac_zicsr_zifencei  -Wall -Wextra -Wwrite-strings -Wmissing-prototypes -Wformat=2 -Wno-format-nonliteral -Wvla -Wlogical-op -Wshadow    -Wformat-signedness -o CMakeFiles/cmTC_94877.dir/src.c.obj -c C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/CMakeFiles/CMakeScratch/TryCompile-ozgsbe/src.c
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe -march=rv32imac_zicsr_zifencei  -Wall -Wextra -Wwrite-strings -Wmissing-prototypes -Wformat=2 -Wno-format-nonliteral -Wvla -Wlogical-op -Wshadow -nostartfiles -march=rv32imac_zicsr_zifencei CMakeFiles/cmTC_94877.dir/src.c.obj -o cmTC_94877   && cd ."
        C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/bin/ld.exe: warning: cannot find entry symbol _start; defaulting to 00010094
        
      exitCode: 0
...
