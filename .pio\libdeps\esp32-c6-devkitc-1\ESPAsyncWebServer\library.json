{"name": "ESPAsyncWebServer", "version": "3.6.0", "description": "Asynchronous HTTP and WebSocket Server Library for ESP32, ESP8266 and RP2040. Supports: WebSocket, SSE, Authentication, Arduino Json 7, File Upload, Static File serving, URL Rewrite, URL Redirect, etc.", "keywords": "http,async,websocket,webserver", "homepage": "https://github.com/ESP32Async/ESPAsyncWebServer", "repository": {"type": "git", "url": "https://github.com/ESP32Async/ESPAsyncWebServer.git"}, "authors": {"name": "ESP32Async", "maintainer": true}, "license": "LGPL-3.0", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["espressif32", "espressif8266", "raspberry<PERSON>"], "dependencies": [{"owner": "ESP32Async", "name": "AsyncTCP", "version": "^3.3.2", "platforms": "espressif32"}, {"owner": "ESP32Async", "name": "ESPAsyncTCP", "version": "^2.0.0", "platforms": "espressif8266"}, {"name": "Hash", "platforms": "espressif8266"}, {"owner": "khoih-prog", "name": "AsyncTCP_RP2040W", "version": "^1.2.0", "platforms": "raspberry<PERSON>"}], "export": {"include": ["examples", "src", "library.json", "library.properties", "LICENSE", "README.md"]}}