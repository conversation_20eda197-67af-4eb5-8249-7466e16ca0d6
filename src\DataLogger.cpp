#include "DataLogger.h"
#include <time.h>

DataLogger::DataLogger() {
  loggingEnabled = true;
  maxLogSize = 1024 * 1024; // 1MB per log file
  maxLogFiles = 10;
}

void DataLogger::begin() {
  currentLogFile = generateLogFileName();
}

void DataLogger::enableLogging(bool enable) {
  loggingEnabled = enable;
}

bool DataLogger::isLoggingEnabled() {
  return loggingEnabled;
}

String DataLogger::generateLogFileName() {
  time_t now;
  time(&now);
  struct tm* timeinfo = localtime(&now);
  
  char filename[32];
  strftime(filename, sizeof(filename), "/logs/data_%Y%m%d.log", timeinfo);
  return String(filename);
}

bool DataLogger::logSensorReading(String sensorId, String readingName, float value, String unit) {
  if (!loggingEnabled) return false;
  
  DataPoint point;
  point.timestamp = millis();
  point.sensorId = sensorId;
  point.readingName = readingName;
  point.value = value;
  point.unit = unit;
  
  return writeDataPoint(point);
}

bool DataLogger::logCurrentData(JsonDocument sensorData) {
  if (!loggingEnabled) return false;
  
  JsonArray sensors = sensorData["sensors"];
  for (JsonObject sensor : sensors) {
    String sensorId = sensor["id"];
    JsonObject readings = sensor["readings"];
    
    for (JsonPair reading : readings) {
      String readingName = reading.key().c_str();
      JsonObject readingObj = reading.value();
      float value = readingObj["value"];
      String unit = readingObj["unit"];
      
      logSensorReading(sensorId, readingName, value, unit);
    }
  }
  
  return true;
}

bool DataLogger::writeDataPoint(DataPoint point) {
  // Create logs directory if it doesn't exist
  if (!SPIFFS.exists("/logs")) {
    // SPIFFS doesn't support mkdir, so we'll create a dummy file to establish the path
  }
  
  File file = SPIFFS.open(currentLogFile, "a");
  if (!file) {
    return false;
  }
  
  // Write data point as JSON line
  JsonDocument doc;
  doc["timestamp"] = point.timestamp;
  doc["sensor"] = point.sensorId;
  doc["reading"] = point.readingName;
  doc["value"] = point.value;
  doc["unit"] = point.unit;
  
  serializeJson(doc, file);
  file.println();
  file.close();
  
  // Check if we need to rotate log files
  if (file.size() > maxLogSize) {
    rotateLogFiles();
  }
  
  return true;
}

void DataLogger::rotateLogFiles() {
  // Simple rotation - generate new filename
  currentLogFile = generateLogFileName();
}

JsonDocument DataLogger::getHistoricalData(String sensorId, String readingName, unsigned long startTime, unsigned long endTime) {
  JsonDocument result;
  JsonArray dataArray = result["data"].to<JsonArray>();
  
  File file = SPIFFS.open(currentLogFile, "r");
  if (!file) {
    return result;
  }
  
  while (file.available()) {
    String line = file.readStringUntil('\n');
    if (line.length() > 0) {
      JsonDocument doc;
      deserializeJson(doc, line);
      
      unsigned long timestamp = doc["timestamp"];
      String sensor = doc["sensor"];
      String reading = doc["reading"];
      
      if (sensor == sensorId && reading == readingName && 
          timestamp >= startTime && timestamp <= endTime) {
        JsonObject dataPoint = dataArray.add<JsonObject>();
        dataPoint["timestamp"] = timestamp;
        dataPoint["value"] = doc["value"];
        dataPoint["unit"] = doc["unit"];
      }
    }
  }
  
  file.close();
  return result;
}

JsonDocument DataLogger::getRecentData(String sensorId, String readingName, int hours) {
  unsigned long endTime = millis();
  unsigned long startTime = endTime - (hours * 3600 * 1000); // Convert hours to milliseconds
  
  return getHistoricalData(sensorId, readingName, startTime, endTime);
}

void DataLogger::clearLogs() {
  // Remove all log files
  File root = SPIFFS.open("/logs");
  if (root && root.isDirectory()) {
    File file = root.openNextFile();
    while (file) {
      if (String(file.name()).endsWith(".log")) {
        SPIFFS.remove(file.name());
      }
      file = root.openNextFile();
    }
  }
  
  // Reset current log file
  currentLogFile = generateLogFileName();
}

JsonDocument DataLogger::getLogFileInfo() {
  JsonDocument info;
  JsonArray files = info["files"].to<JsonArray>();
  
  File root = SPIFFS.open("/logs");
  if (root && root.isDirectory()) {
    File file = root.openNextFile();
    while (file) {
      if (String(file.name()).endsWith(".log")) {
        JsonObject fileInfo = files.add<JsonObject>();
        fileInfo["name"] = file.name();
        fileInfo["size"] = file.size();
      }
      file = root.openNextFile();
    }
  }
  
  info["totalSize"] = getTotalLogSize();
  info["maxSize"] = maxLogSize;
  info["maxFiles"] = maxLogFiles;
  
  return info;
}

unsigned long DataLogger::getTotalLogSize() {
  unsigned long totalSize = 0;
  
  File root = SPIFFS.open("/logs");
  if (root && root.isDirectory()) {
    File file = root.openNextFile();
    while (file) {
      if (String(file.name()).endsWith(".log")) {
        totalSize += file.size();
      }
      file = root.openNextFile();
    }
  }
  
  return totalSize;
}
