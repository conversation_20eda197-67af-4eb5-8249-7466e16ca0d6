{"archive": {}, "artifacts": [{"path": "esp-idf/esp_local_ctrl/libesp_local_ctrl.a"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_library", "idf_component_register", "set_property", "__component_set_dependencies", "__component_set_all_dependencies", "add_compile_options", "add_c_compile_options", "add_compile_definitions", "include_directories", "target_include_directories", "__component_add_include_dirs"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/component.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/utilities.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 23, "parent": 0}, {"command": 0, "file": 0, "line": 491, "parent": 1}, {"command": 4, "file": 0, "line": 524, "parent": 1}, {"command": 3, "file": 0, "line": 359, "parent": 3}, {"command": 2, "file": 0, "line": 344, "parent": 4}, {"command": 2, "file": 0, "line": 344, "parent": 4}, {"command": 5, "file": 0, "line": 476, "parent": 1}, {"command": 6, "file": 0, "line": 478, "parent": 1}, {"command": 5, "file": 2, "line": 281, "parent": 8}, {"command": 7, "file": 0, "line": 477, "parent": 1}, {"command": 8, "file": 0, "line": 475, "parent": 1}, {"command": 10, "file": 0, "line": 493, "parent": 1}, {"command": 9, "file": 0, "line": 320, "parent": 12}, {"command": 10, "file": 0, "line": 494, "parent": 1}, {"command": 9, "file": 0, "line": 320, "parent": 14}, {"command": 9, "file": 0, "line": 320, "parent": 14}, {"command": 3, "file": 0, "line": 362, "parent": 3}, {"command": 2, "file": 0, "line": 341, "parent": 17}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always"}, {"backtrace": 7, "fragment": "-ffunction-sections"}, {"backtrace": 7, "fragment": "-fdata-sections"}, {"backtrace": 7, "fragment": "-Wall"}, {"backtrace": 7, "fragment": "-Werror=all"}, {"backtrace": 7, "fragment": "-Wno-error=unused-function"}, {"backtrace": 7, "fragment": "-Wno-error=unused-variable"}, {"backtrace": 7, "fragment": "-Wno-error=unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-error=deprecated-declarations"}, {"backtrace": 7, "fragment": "-Wextra"}, {"backtrace": 7, "fragment": "-Wno-error=extra"}, {"backtrace": 7, "fragment": "-Wno-unused-parameter"}, {"backtrace": 7, "fragment": "-Wno-sign-compare"}, {"backtrace": 7, "fragment": "-Wno-enum-conversion"}, {"backtrace": 7, "fragment": "-gdwarf-4"}, {"backtrace": 7, "fragment": "-ggdb"}, {"backtrace": 7, "fragment": "-nostartfiles"}, {"backtrace": 7, "fragment": "-Og"}, {"backtrace": 7, "fragment": "-fno-shrink-wrap"}, {"backtrace": 7, "fragment": "-fmacro-prefix-map=C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver=."}, {"backtrace": 7, "fragment": "-fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf=/IDF"}, {"backtrace": 7, "fragment": "-fstrict-volatile-bitfields"}, {"backtrace": 7, "fragment": "-fno-jump-tables"}, {"backtrace": 7, "fragment": "-fno-tree-switch-conversion"}, {"backtrace": 9, "fragment": "-std=gnu17"}, {"backtrace": 9, "fragment": "-Wno-old-style-declaration"}], "defines": [{"backtrace": 10, "define": "ESP_PLATFORM"}, {"backtrace": 10, "define": "IDF_VER=\"5.5.0\""}, {"backtrace": 6, "define": "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\""}, {"backtrace": 2, "define": "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE"}, {"backtrace": 2, "define": "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ"}, {"backtrace": 10, "define": "_GLIBCXX_HAVE_POSIX_SEMAPHORE"}, {"backtrace": 10, "define": "_GLIBCXX_USE_POSIX_SEMAPHORE"}, {"backtrace": 10, "define": "_GNU_SOURCE"}, {"backtrace": 10, "define": "_POSIX_READER_WRITER_LOCKS"}], "includes": [{"backtrace": 11, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/config"}, {"backtrace": 13, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/include"}, {"backtrace": 15, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/proto-c"}, {"backtrace": 16, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/src"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/platform_include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include/freertos"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/riscv/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/riscv/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/riscv/include/freertos"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/esp_additions/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/dma/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/ldo/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/debug_probe/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/power_supply/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/."}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/private_include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/tlsf"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/log/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/register"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/platform_port/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/soc"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/include/riscv"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/include/private"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/riscv/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps/sntp"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/lwip/src/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/freertos/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/arch"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/sys"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/common"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/security"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/transports"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/crypto/srp6a"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/proto-c"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_timer/include"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/include"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/include/local"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/wifi_apps/include"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/wifi_apps/nan_app/include"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_event/include"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_phy/include"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_phy/esp32c6/include"}, {"backtrace": 5, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_netif/include"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_https_server/include"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_http_server/include"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/http_parser"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp-tls"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp-tls/esp-tls-crypto"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/port/include"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/include"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/library"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/esp_crt_bundle/include"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/everest/include"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/p256-m"}, {"backtrace": 6, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m"}, {"backtrace": 18, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/protobuf-c/protobuf-c"}], "language": "C", "sourceIndexes": [0, 1, 2, 3]}], "dependencies": [{"backtrace": 5, "id": "__idf_console::@4311ed8d271cc0ee1422"}, {"backtrace": 6, "id": "__idf_esp_https_server::@a47490a9e49b7eabdb18"}, {"backtrace": 2, "id": "__idf_riscv::@5eeb1a2ca709d020776e"}, {"backtrace": 5, "id": "__idf_protobuf-c::@b14513c4bd859268ec22"}, {"backtrace": 5, "id": "__idf_protocomm::@d76d01c8dec06205e1fc"}], "id": "__idf_esp_local_ctrl::@d6aad58d2946e56208ef", "name": "__idf_esp_local_ctrl", "nameOnDisk": "libesp_local_ctrl.a", "paths": {"build": "esp-idf/esp_local_ctrl", "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/src/esp_local_ctrl.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}