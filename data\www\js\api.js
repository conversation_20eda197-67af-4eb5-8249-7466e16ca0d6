// API Helper functions for ESP32 Environmental Monitor
class APIClient {
    constructor() {
        this.baseURL = '';
        this.timeout = 10000; // 10 seconds
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Request timeout');
            }
            throw error;
        }
    }

    // System Status API
    async getSystemStatus() {
        return this.request('/api/status');
    }

    // Sensor Data API
    async getSensorData() {
        return this.request('/api/sensors');
    }

    async getSensorConfig() {
        return this.request('/api/sensors/config');
    }

    async updateSensorConfig(config) {
        return this.request('/api/sensors/config', {
            method: 'POST',
            body: JSON.stringify(config)
        });
    }

    async addSensor(sensorData) {
        return this.request('/api/sensors/add', {
            method: 'POST',
            body: JSON.stringify(sensorData)
        });
    }

    async removeSensor(sensorId) {
        return this.request('/api/sensors/remove', {
            method: 'DELETE',
            body: JSON.stringify({ id: sensorId })
        });
    }

    // Historical Data API
    async getHistoricalData(sensorId, readingName, timeframe = '24h') {
        const params = new URLSearchParams({
            sensor: sensorId,
            reading: readingName,
            timeframe: timeframe
        });
        return this.request(`/api/history?${params}`);
    }

    async getRecentData(timeframe = '24h') {
        const params = new URLSearchParams({ timeframe });
        return this.request(`/api/history?${params}`);
    }

    // Settings API
    async getSettings() {
        return this.request('/api/settings');
    }

    async updateSettings(settings) {
        return this.request('/api/settings', {
            method: 'POST',
            body: JSON.stringify(settings)
        });
    }

    // Utility methods
    formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    formatUptime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}d ${hours % 24}h ${minutes % 60}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else {
            return `${minutes}m ${seconds % 60}s`;
        }
    }

    getSignalStrengthText(rssi) {
        if (rssi >= -50) return 'Excellent';
        if (rssi >= -60) return 'Good';
        if (rssi >= -70) return 'Fair';
        return 'Poor';
    }

    getAirQualityLevel(value) {
        if (value <= 20) return { level: 'Excellent', color: '#10b981' };
        if (value <= 40) return { level: 'Good', color: '#22c55e' };
        if (value <= 60) return { level: 'Moderate', color: '#f59e0b' };
        if (value <= 80) return { level: 'Poor', color: '#f97316' };
        return { level: 'Hazardous', color: '#ef4444' };
    }
}

// Sensor Management Helper
class SensorManager {
    constructor(apiClient) {
        this.api = apiClient;
        this.sensors = [];
        this.sensorTypes = [
            { id: 'DHT22', name: 'DHT22 - Temperature/Humidity', category: 'environmental' },
            { id: 'MQ135', name: 'MQ-135 - Air Quality', category: 'gas' },
            { id: 'MQ2', name: 'MQ-2 - Smoke/Gas', category: 'gas' },
            { id: 'MQ3', name: 'MQ-3 - Alcohol', category: 'gas' },
            { id: 'MQ4', name: 'MQ-4 - Methane', category: 'gas' },
            { id: 'MQ5', name: 'MQ-5 - LPG', category: 'gas' },
            { id: 'MQ6', name: 'MQ-6 - LPG/Butane', category: 'gas' },
            { id: 'MQ7', name: 'MQ-7 - Carbon Monoxide', category: 'gas' },
            { id: 'MQ8', name: 'MQ-8 - Hydrogen', category: 'gas' },
            { id: 'MQ9', name: 'MQ-9 - CO/Methane', category: 'gas' },
            { id: 'BMP280', name: 'BMP280 - Pressure/Temperature', category: 'environmental' },
            { id: 'BME280', name: 'BME280 - Pressure/Temperature/Humidity', category: 'environmental' }
        ];
    }

    async loadSensors() {
        try {
            const data = await this.api.getSensorConfig();
            this.sensors = data.sensors || [];
            return this.sensors;
        } catch (error) {
            console.error('Error loading sensors:', error);
            return [];
        }
    }

    async addSensor(sensorData) {
        try {
            const result = await this.api.addSensor(sensorData);
            if (result.status === 'success') {
                await this.loadSensors(); // Reload sensors
            }
            return result;
        } catch (error) {
            console.error('Error adding sensor:', error);
            throw error;
        }
    }

    async removeSensor(sensorId) {
        try {
            const result = await this.api.removeSensor(sensorId);
            if (result.status === 'success') {
                await this.loadSensors(); // Reload sensors
            }
            return result;
        } catch (error) {
            console.error('Error removing sensor:', error);
            throw error;
        }
    }

    getSensorTypes() {
        return this.sensorTypes;
    }

    getSensorTypesByCategory(category) {
        return this.sensorTypes.filter(type => type.category === category);
    }

    validateSensorData(sensorData) {
        const errors = [];

        if (!sensorData.id || sensorData.id.trim() === '') {
            errors.push('Sensor ID is required');
        }

        if (!sensorData.type || sensorData.type.trim() === '') {
            errors.push('Sensor type is required');
        }

        if (sensorData.pin === undefined || sensorData.pin === null) {
            errors.push('Pin number is required');
        }

        if (sensorData.pin < 0 || sensorData.pin > 39) {
            errors.push('Pin number must be between 0 and 39');
        }

        // Check if sensor ID already exists
        if (this.sensors.some(sensor => sensor.id === sensorData.id)) {
            errors.push('Sensor ID already exists');
        }

        // Check if pin is already in use
        if (this.sensors.some(sensor => sensor.pin === sensorData.pin)) {
            errors.push('Pin is already in use by another sensor');
        }

        return errors;
    }
}

// Data Visualization Helper
class DataVisualizer {
    constructor() {
        this.colorPalette = [
            '#2563eb', '#06b6d4', '#10b981', '#f59e0b', 
            '#ef4444', '#8b5cf6', '#ec4899', '#14b8a6'
        ];
    }

    generateChartData(sensorData, timeframe = '24h') {
        // Process sensor data for chart visualization
        const datasets = [];
        let colorIndex = 0;

        Object.keys(sensorData).forEach(sensorId => {
            const sensor = sensorData[sensorId];
            Object.keys(sensor.readings || {}).forEach(readingName => {
                const reading = sensor.readings[readingName];
                datasets.push({
                    label: `${sensorId} - ${readingName}`,
                    data: reading.history || [],
                    borderColor: this.colorPalette[colorIndex % this.colorPalette.length],
                    backgroundColor: this.colorPalette[colorIndex % this.colorPalette.length] + '20',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                });
                colorIndex++;
            });
        });

        return { datasets };
    }

    formatValue(value, unit) {
        if (typeof value !== 'number') return '--';
        
        switch (unit) {
            case '°C':
            case '%':
                return value.toFixed(1);
            case 'hPa':
                return value.toFixed(0);
            default:
                return value.toFixed(2);
        }
    }
}

// Initialize API client and make it globally available
document.addEventListener('DOMContentLoaded', () => {
    window.apiClient = new APIClient();
    window.sensorManager = new SensorManager(window.apiClient);
    window.dataVisualizer = new DataVisualizer();
});
