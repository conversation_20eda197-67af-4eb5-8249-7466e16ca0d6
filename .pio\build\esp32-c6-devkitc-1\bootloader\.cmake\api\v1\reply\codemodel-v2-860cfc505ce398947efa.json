{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [14, 15, 16, 18, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36]}, {"build": "esp-idf", "childIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25], "jsonFile": "directory-esp-idf-4ce53965c45cd09d05cc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf"}, {"build": "esp-idf/hal", "jsonFile": "directory-esp-idf.hal-bef9d6bec9341a508ffa.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal", "targetIndexes": [7]}, {"build": "esp-idf/riscv", "jsonFile": "directory-esp-idf.riscv-c81ec38e3e1513f4dddd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/riscv", "targetIndexes": [11]}, {"build": "esp-idf/newlib", "jsonFile": "directory-esp-idf.newlib-697528d976c5859bfdf4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib"}, {"build": "esp-idf/soc", "jsonFile": "directory-esp-idf.soc-b2ac3d6eaf3b150286a8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc", "targetIndexes": [12]}, {"build": "esp-idf/micro-ecc", "jsonFile": "directory-esp-idf.micro-ecc-b84f620316c40fe9b692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "components/micro-ecc", "targetIndexes": [10]}, {"build": "esp-idf/spi_flash", "jsonFile": "directory-esp-idf.spi_flash-68ce979caab053ea2373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash", "targetIndexes": [13]}, {"build": "esp-idf/esp_bootloader_format", "jsonFile": "directory-esp-idf.esp_bootloader_format-7b67763630a0df99a6c9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_bootloader_format", "targetIndexes": [2]}, {"build": "esp-idf/esp_app_format", "jsonFile": "directory-esp-idf.esp_app_format-8ea29cb05bcecbeae4ad.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_app_format"}, {"build": "esp-idf/bootloader_support", "jsonFile": "directory-esp-idf.bootloader_support-21284ae9f949cf8c44e2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support", "targetIndexes": [0]}, {"build": "esp-idf/efuse", "jsonFile": "directory-esp-idf.efuse-8822af0d1d07f1bf0039.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse", "targetIndexes": [1, 19, 20, 21, 22, 23, 30, 31]}, {"build": "esp-idf/esp_security", "jsonFile": "directory-esp-idf.esp_security-026b18d19f7b30dcf270.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_security"}, {"build": "esp-idf/esp_system", "jsonFile": "directory-esp-idf.esp_system-e4254296185676bee716.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system", "targetIndexes": [6]}, {"build": "esp-idf/esp_hw_support", "childIndexes": [15, 16], "jsonFile": "directory-esp-idf.esp_hw_support-dc33cff4e7749e06e3b2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support", "targetIndexes": [4]}, {"build": "esp-idf/esp_hw_support/port/esp32c6", "jsonFile": "directory-esp-idf.esp_hw_support.port.esp32c6-4359866ad5b853018675.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 14, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6"}, {"build": "esp-idf/esp_hw_support/lowpower", "jsonFile": "directory-esp-idf.esp_hw_support.lowpower-50746ddbf0ab606033e2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 14, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/lowpower"}, {"build": "esp-idf/esp_common", "jsonFile": "directory-esp-idf.esp_common-7842d604b4865c5a55ca.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common", "targetIndexes": [3]}, {"build": "esp-idf/esp_rom", "jsonFile": "directory-esp-idf.esp_rom-2dc702ce4d5e9527a534.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom", "targetIndexes": [5]}, {"build": "esp-idf/log", "jsonFile": "directory-esp-idf.log-0eda141f6cf94a6441cd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/log", "targetIndexes": [8]}, {"build": "esp-idf/esptool_py", "jsonFile": "directory-esp-idf.esptool_py-d2e51924413bad0b4d2b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py", "targetIndexes": [17]}, {"build": "esp-idf/partition_table", "jsonFile": "directory-esp-idf.partition_table-1925719c98c8b5fd8ce7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/partition_table"}, {"build": "esp-idf/bootloader", "jsonFile": "directory-esp-idf.bootloader-f2802f5c3531041690b5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader"}, {"build": "esp-idf/esp_tee", "jsonFile": "directory-esp-idf.esp_tee-df36a07b609fe93636b7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_tee"}, {"build": "esp-idf/freertos", "jsonFile": "directory-esp-idf.freertos-6ec356af6cab6a318b2f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos"}, {"build": "esp-idf/main", "jsonFile": "directory-esp-idf.main-5bff74fee2a922659b4e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "main", "targetIndexes": [9]}], "name": "", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "bootloader", "targetIndexes": [14, 15, 16, 18, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36]}, {"directoryIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "name": "esp-idf", "parentIndex": 0, "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 17, 19, 20, 21, 22, 23, 30, 31]}], "targets": [{"directoryIndex": 10, "id": "__idf_bootloader_support::@d88583f91142925c6676", "jsonFile": "target-__idf_bootloader_support-820843f852a035bbe398.json", "name": "__idf_bootloader_support", "projectIndex": 1}, {"directoryIndex": 11, "id": "__idf_efuse::@2da764c922f8b73e9c75", "jsonFile": "target-__idf_efuse-d2122bd607d71ceb35d4.json", "name": "__idf_efuse", "projectIndex": 1}, {"directoryIndex": 8, "id": "__idf_esp_bootloader_format::@d2aa1b1547bed85670ee", "jsonFile": "target-__idf_esp_bootloader_format-e0487ad8fde48a112b3b.json", "name": "__idf_esp_bootloader_format", "projectIndex": 1}, {"directoryIndex": 17, "id": "__idf_esp_common::@5ba0aac035ae9b4e20a4", "jsonFile": "target-__idf_esp_common-4031b36f75fceafe14e2.json", "name": "__idf_esp_common", "projectIndex": 1}, {"directoryIndex": 14, "id": "__idf_esp_hw_support::@ce4791f5a7554c569ddb", "jsonFile": "target-__idf_esp_hw_support-49c678960b8f945bbe5a.json", "name": "__idf_esp_hw_support", "projectIndex": 1}, {"directoryIndex": 18, "id": "__idf_esp_rom::@c269e361cd87ad81e927", "jsonFile": "target-__idf_esp_rom-a00c695c3b2b99fbfd05.json", "name": "__idf_esp_rom", "projectIndex": 1}, {"directoryIndex": 13, "id": "__idf_esp_system::@874df54e61e20112dfd4", "jsonFile": "target-__idf_esp_system-0e30e8ec0cc81e900206.json", "name": "__idf_esp_system", "projectIndex": 1}, {"directoryIndex": 2, "id": "__idf_hal::@41c1a00f56cd1e9ede18", "jsonFile": "target-__idf_hal-6e53f416753ae4d62e20.json", "name": "__idf_hal", "projectIndex": 1}, {"directoryIndex": 19, "id": "__idf_log::@d5e1aa0ef4e3c98c8a70", "jsonFile": "target-__idf_log-1c3d97fd42801435dcdb.json", "name": "__idf_log", "projectIndex": 1}, {"directoryIndex": 25, "id": "__idf_main::@7368607ed66887415643", "jsonFile": "target-__idf_main-617357a58550b63193b7.json", "name": "__idf_main", "projectIndex": 1}, {"directoryIndex": 6, "id": "__idf_micro-ecc::@37158dc68a69c79f835a", "jsonFile": "target-__idf_micro-ecc-a981f6ac87222591279c.json", "name": "__idf_micro-ecc", "projectIndex": 1}, {"directoryIndex": 3, "id": "__idf_riscv::@5eeb1a2ca709d020776e", "jsonFile": "target-__idf_riscv-74b3b6d0fd29d03bcfb5.json", "name": "__idf_riscv", "projectIndex": 1}, {"directoryIndex": 5, "id": "__idf_soc::@824f1541829f13857909", "jsonFile": "target-__idf_soc-5b283fcb154ec03b41ee.json", "name": "__idf_soc", "projectIndex": 1}, {"directoryIndex": 7, "id": "__idf_spi_flash::@6f8a8c72478269b2cd60", "jsonFile": "target-__idf_spi_flash-4f7ec31f1a7f57a08a26.json", "name": "__idf_spi_flash", "projectIndex": 1}, {"directoryIndex": 0, "id": "_project_elf_src::@6890427a1f51a3e7e1df", "jsonFile": "target-_project_elf_src-300eb97a70b73131df1e.json", "name": "_project_elf_src", "projectIndex": 0}, {"directoryIndex": 0, "id": "app::@6890427a1f51a3e7e1df", "jsonFile": "target-app-86963502db6aae7c90aa.json", "name": "app", "projectIndex": 0}, {"directoryIndex": 0, "id": "bootloader.elf::@6890427a1f51a3e7e1df", "jsonFile": "target-bootloader.elf-1ad2aab0cccb2c8de735.json", "name": "bootloader.elf", "projectIndex": 0}, {"directoryIndex": 20, "id": "bootloader_check_size::@bf8f55be585a12323665", "jsonFile": "target-bootloader_check_size-b9027cf9ba41fcd3c3d5.json", "name": "bootloader_check_size", "projectIndex": 1}, {"directoryIndex": 0, "id": "confserver::@6890427a1f51a3e7e1df", "jsonFile": "target-confserver-64afe1276d1c45f2a852.json", "name": "confserver", "projectIndex": 0}, {"directoryIndex": 11, "id": "efuse-common-table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse-common-table-6ee3053919c145924808.json", "name": "efuse-common-table", "projectIndex": 1}, {"directoryIndex": 11, "id": "efuse-custom-table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse-custom-table-ea03126234e6cc25d23c.json", "name": "efuse-custom-table", "projectIndex": 1}, {"directoryIndex": 11, "id": "efuse_common_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_common_table-ab1aec4aaf76aeac4cd4.json", "name": "efuse_common_table", "projectIndex": 1}, {"directoryIndex": 11, "id": "efuse_custom_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_custom_table-ffca90eeee27dbc0e830.json", "name": "efuse_custom_table", "projectIndex": 1}, {"directoryIndex": 11, "id": "efuse_test_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_test_table-c9e21dbc943e74b24511.json", "name": "efuse_test_table", "projectIndex": 1}, {"directoryIndex": 0, "id": "erase_flash::@6890427a1f51a3e7e1df", "jsonFile": "target-erase_flash-47785ca2b61b3264d1a4.json", "name": "erase_flash", "projectIndex": 0}, {"directoryIndex": 0, "id": "gen_project_binary::@6890427a1f51a3e7e1df", "jsonFile": "target-gen_project_binary-237ef22d538da3fd5a76.json", "name": "gen_project_binary", "projectIndex": 0}, {"directoryIndex": 0, "id": "menuconfig::@6890427a1f51a3e7e1df", "jsonFile": "target-menuconfig-a7f067be196e0662ea45.json", "name": "menuconfig", "projectIndex": 0}, {"directoryIndex": 0, "id": "merge-bin::@6890427a1f51a3e7e1df", "jsonFile": "target-merge-bin-fb0605796b6d928180e9.json", "name": "merge-bin", "projectIndex": 0}, {"directoryIndex": 0, "id": "monitor::@6890427a1f51a3e7e1df", "jsonFile": "target-monitor-5fd2f490600ccde1714d.json", "name": "monitor", "projectIndex": 0}, {"directoryIndex": 0, "id": "save-defconfig::@6890427a1f51a3e7e1df", "jsonFile": "target-save-defconfig-8574730af255da81ffd2.json", "name": "save-defconfig", "projectIndex": 0}, {"directoryIndex": 11, "id": "show-efuse-table::@2da764c922f8b73e9c75", "jsonFile": "target-show-efuse-table-d3302a61dbbc185e59e1.json", "name": "show-efuse-table", "projectIndex": 1}, {"directoryIndex": 11, "id": "show_efuse_table::@2da764c922f8b73e9c75", "jsonFile": "target-show_efuse_table-af3d6cd1e9687a3cf93b.json", "name": "show_efuse_table", "projectIndex": 1}, {"directoryIndex": 0, "id": "size::@6890427a1f51a3e7e1df", "jsonFile": "target-size-9584aada743b3724e7ee.json", "name": "size", "projectIndex": 0}, {"directoryIndex": 0, "id": "size-components::@6890427a1f51a3e7e1df", "jsonFile": "target-size-components-ba1d7bfd240329811a05.json", "name": "size-components", "projectIndex": 0}, {"directoryIndex": 0, "id": "size-files::@6890427a1f51a3e7e1df", "jsonFile": "target-size-files-794c850bdc6e39cb7a73.json", "name": "size-files", "projectIndex": 0}, {"directoryIndex": 0, "id": "uf2::@6890427a1f51a3e7e1df", "jsonFile": "target-uf2-c8039749544ba1f7720d.json", "name": "uf2", "projectIndex": 0}, {"directoryIndex": 0, "id": "uf2-app::@6890427a1f51a3e7e1df", "jsonFile": "target-uf2-app-abcb12f057b01a81ad9f.json", "name": "uf2-app", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader", "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader/subproject"}, "version": {"major": 2, "minor": 7}}