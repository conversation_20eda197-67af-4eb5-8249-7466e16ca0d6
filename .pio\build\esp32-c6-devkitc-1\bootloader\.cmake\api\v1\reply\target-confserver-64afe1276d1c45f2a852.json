{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "__kconfig_generate_config", "idf_build_process", "project"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/kconfig.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/build.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 72, "parent": 0}, {"command": 2, "file": 2, "line": 740, "parent": 1}, {"command": 1, "file": 1, "line": 704, "parent": 2}, {"command": 0, "file": 0, "line": 287, "parent": 3}]}, "id": "confserver::@6890427a1f51a3e7e1df", "name": "confserver", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/CMakeFiles/confserver", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/CMakeFiles/confserver.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}