{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/.platformio/packages/tool-cmake/bin/cmake.exe", "cpack": "C:/Users/<USER>/.platformio/packages/tool-cmake/bin/cpack.exe", "ctest": "C:/Users/<USER>/.platformio/packages/tool-cmake/bin/ctest.exe", "root": "C:/Users/<USER>/.platformio/packages/tool-cmake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 2, "string": "3.30.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-860cfc505ce398947efa.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}], "reply": {"codemodel-v2": {"jsonFile": "codemodel-v2-860cfc505ce398947efa.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}