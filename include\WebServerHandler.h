#ifndef WEB_SERVER_HANDLER_H
#define WEB_SERVER_HANDLER_H

#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include "SensorManager.h"

class WebServerHandler {
private:
  AsyncWebServer* server;
  SensorManager* sensorManager;
  
  // Helper methods
  String getContentType(String filename);
  void handleNotFound(AsyncWebServerRequest *request);
  
  // API endpoints
  void handleGetSensorData(AsyncWebServerRequest *request);
  void handleGetSystemStatus(AsyncWebServerRequest *request);
  void handleGetSensorConfig(AsyncWebServerRequest *request);
  void handleUpdateSensorConfig(AsyncWebServerRequest *request);
  void handleAddSensor(AsyncWebServerRequest *request);
  void handleRemoveSensor(AsyncWebServerRequest *request);
  void handleGetHistoricalData(AsyncWebServerRequest *request);
  void handleGetSettings(AsyncWebServerRequest *request);
  void handleUpdateSettings(AsyncWebServerRequest *request);
  
public:
  WebServerHandler(AsyncWebServer* srv, SensorManager* sm);
  void begin();
  void setupRoutes();
  void setupStaticFiles();
  void setupAPIRoutes();
};

#endif
