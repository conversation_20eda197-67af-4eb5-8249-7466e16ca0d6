#include "WebServerHandler.h"
#include <SPIFFS.h>
#include <time.h>

WebServerHandler::WebServerHandler(AsyncWebServer* srv, SensorManager* sm) {
  server = srv;
  sensorManager = sm;
}

void WebServerHandler::begin() {
  setupStaticFiles();
  setupAPIRoutes();
  server->onNotFound([this](AsyncWebServerRequest *request) {
    handleNotFound(request);
  });
  server->begin();
}

void WebServerHandler::setupStaticFiles() {
  // Serve static files from SPIFFS
  server->serveStatic("/", SPIFFS, "/www/").setDefaultFile("index.html");
  
  // Enable CORS for API calls
  DefaultHeaders::Instance().addHeader("Access-Control-Allow-Origin", "*");
  DefaultHeaders::Instance().addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
  DefaultHeaders::Instance().addHeader("Access-Control-Allow-Headers", "Content-Type");
}

void WebServerHandler::setupAPIRoutes() {
  // System status
  server->on("/api/status", HTTP_GET, [this](AsyncWebServerRequest *request) {
    handleGetSystemStatus(request);
  });
  
  // Sensor data
  server->on("/api/sensors", HTTP_GET, [this](AsyncWebServerRequest *request) {
    handleGetSensorData(request);
  });
  
  // Sensor configuration
  server->on("/api/sensors/config", HTTP_GET, [this](AsyncWebServerRequest *request) {
    handleGetSensorConfig(request);
  });
  
  server->on("/api/sensors/config", HTTP_POST, [this](AsyncWebServerRequest *request) {
    handleUpdateSensorConfig(request);
  });
  
  server->on("/api/sensors/add", HTTP_POST, [this](AsyncWebServerRequest *request) {
    handleAddSensor(request);
  });
  
  server->on("/api/sensors/remove", HTTP_DELETE, [this](AsyncWebServerRequest *request) {
    handleRemoveSensor(request);
  });
  
  // Historical data
  server->on("/api/history", HTTP_GET, [this](AsyncWebServerRequest *request) {
    handleGetHistoricalData(request);
  });
  
  // Settings
  server->on("/api/settings", HTTP_GET, [this](AsyncWebServerRequest *request) {
    handleGetSettings(request);
  });
  
  server->on("/api/settings", HTTP_POST, [this](AsyncWebServerRequest *request) {
    handleUpdateSettings(request);
  });
}

void WebServerHandler::handleGetSystemStatus(AsyncWebServerRequest *request) {
  JsonDocument doc;
  
  // System info
  doc["uptime"] = millis();
  doc["freeHeap"] = ESP.getFreeHeap();
  doc["totalHeap"] = ESP.getHeapSize();
  doc["chipModel"] = ESP.getChipModel();
  doc["chipRevision"] = ESP.getChipRevision();
  doc["flashSize"] = ESP.getFlashChipSize();
  
  // WiFi info
  doc["wifi"]["connected"] = WiFi.status() == WL_CONNECTED;
  doc["wifi"]["ssid"] = WiFi.SSID();
  doc["wifi"]["ip"] = WiFi.localIP().toString();
  doc["wifi"]["rssi"] = WiFi.RSSI();
  
  // Time
  time_t now;
  time(&now);
  doc["timestamp"] = now;
  
  // Sensor status
  doc["sensors"]["total"] = sensorManager->getTotalSensorCount();
  doc["sensors"]["online"] = sensorManager->getOnlineSensorCount();
  
  // Data logging status
  doc["dataLogging"]["enabled"] = true; // Will be updated when DataLogger is implemented
  
  String response;
  serializeJson(doc, response);
  request->send(200, "application/json", response);
}

void WebServerHandler::handleGetSensorData(AsyncWebServerRequest *request) {
  JsonDocument sensorData = sensorManager->getAllSensorData();
  String response;
  serializeJson(sensorData, response);
  request->send(200, "application/json", response);
}

void WebServerHandler::handleGetSensorConfig(AsyncWebServerRequest *request) {
  JsonDocument config = sensorManager->getSensorConfig();
  String response;
  serializeJson(config, response);
  request->send(200, "application/json", response);
}

void WebServerHandler::handleUpdateSensorConfig(AsyncWebServerRequest *request) {
  // Implementation for updating sensor configuration
  request->send(200, "application/json", "{\"status\":\"success\"}");
}

void WebServerHandler::handleAddSensor(AsyncWebServerRequest *request) {
  // Implementation for adding new sensor
  request->send(200, "application/json", "{\"status\":\"success\"}");
}

void WebServerHandler::handleRemoveSensor(AsyncWebServerRequest *request) {
  // Implementation for removing sensor
  request->send(200, "application/json", "{\"status\":\"success\"}");
}

void WebServerHandler::handleGetHistoricalData(AsyncWebServerRequest *request) {
  // Implementation for historical data
  request->send(200, "application/json", "{\"data\":[]}");
}

void WebServerHandler::handleGetSettings(AsyncWebServerRequest *request) {
  JsonDocument settings;
  settings["wifi"]["ssid"] = WiFi.SSID();
  settings["dataLogging"]["enabled"] = true;
  settings["dataLogging"]["interval"] = 300; // 5 minutes
  settings["sensors"]["readInterval"] = 30; // 30 seconds
  
  String response;
  serializeJson(settings, response);
  request->send(200, "application/json", response);
}

void WebServerHandler::handleUpdateSettings(AsyncWebServerRequest *request) {
  // Implementation for updating settings
  request->send(200, "application/json", "{\"status\":\"success\"}");
}

void WebServerHandler::handleNotFound(AsyncWebServerRequest *request) {
  request->send(404, "text/plain", "Not found");
}

String WebServerHandler::getContentType(String filename) {
  if (filename.endsWith(".html")) return "text/html";
  else if (filename.endsWith(".css")) return "text/css";
  else if (filename.endsWith(".js")) return "application/javascript";
  else if (filename.endsWith(".json")) return "application/json";
  else if (filename.endsWith(".png")) return "image/png";
  else if (filename.endsWith(".jpg")) return "image/jpeg";
  else if (filename.endsWith(".ico")) return "image/x-icon";
  return "text/plain";
}
