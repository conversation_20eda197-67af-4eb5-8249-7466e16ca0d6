# DHT sensor library [![Build Status](https://github.com/adafruit/DHT-sensor-library/workflows/Arduino%20Library%20CI/badge.svg)](https://github.com/adafruit/DHT-sensor-library/actions)

## Description

An Arduino library for the DHT series of low-cost temperature/humidity sensors.

You can find DHT tutorials [here](https://learn.adafruit.com/dht).

# Dependencies
 * [Adafruit Unified Sensor Driver](https://github.com/adafruit/Adafruit_Sensor)

# Contributing

Contributions are welcome!  Not only you’ll encourage the development of the library, but you’ll also learn how to best use the library and probably some C++ too

Please read our [Code of Conduct](https://github.com/adafruit/DHT-sensor-library/blob/master/CODE_OF_CONDUCT.md>)
before contributing to help this project stay welcoming.

## Documentation and doxygen
Documentation is produced by doxygen. Contributions should include documentation for any new code added.

Some examples of how to use doxygen can be found in these guide pages:

https://learn.adafruit.com/the-well-automated-arduino-library/doxygen

https://learn.adafruit.com/the-well-automated-arduino-library/doxygen-tips

Written by Adafruit Industries based on work by:

 * T. DiCola
 * P. Y. Dragon
 * L. Fried
 * J. Hoffmann
 * M. Kooijman
 * J. M. Dana
 * S. Conaway
 * S. IJskes
 * T. Forbes
 * B. C
 * T. J Myers
 * L. Sørup
 * per1234
 * O. Duffy
 * matthiasdanner
 * J. Lim
 * G. Ambrozio
 * chelmi
 * adams13x13
 * Spacefish
 * I. Scheller
 * C. Miller
 * 7eggert


MIT license, check license.txt for more information
All text above must be included in any redistribution

To install, use the Arduino Library Manager and search for "DHT sensor library" and install the library.
