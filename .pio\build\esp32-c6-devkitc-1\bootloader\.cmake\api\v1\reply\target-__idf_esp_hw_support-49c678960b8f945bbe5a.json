{"archive": {}, "artifacts": [{"path": "esp-idf/esp_hw_support/libesp_hw_support.a"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_library", "idf_component_register", "add_compile_options", "add_c_compile_options", "add_compile_definitions", "include_directories", "target_include_directories", "__component_add_include_dirs", "set_property", "__component_set_dependencies", "__component_set_all_dependencies", "target_sources"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/component.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/utilities.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 187, "parent": 0}, {"command": 0, "file": 0, "line": 491, "parent": 1}, {"command": 2, "file": 0, "line": 476, "parent": 1}, {"command": 3, "file": 0, "line": 478, "parent": 1}, {"command": 2, "file": 2, "line": 281, "parent": 4}, {"command": 4, "file": 0, "line": 477, "parent": 1}, {"command": 5, "file": 0, "line": 475, "parent": 1}, {"command": 7, "file": 0, "line": 493, "parent": 1}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 6, "file": 0, "line": 320, "parent": 8}, {"command": 7, "file": 0, "line": 494, "parent": 1}, {"command": 6, "file": 0, "line": 320, "parent": 18}, {"command": 6, "file": 0, "line": 320, "parent": 18}, {"file": 3}, {"command": 6, "file": 3, "line": 21, "parent": 21}, {"command": 10, "file": 0, "line": 524, "parent": 1}, {"command": 9, "file": 0, "line": 362, "parent": 23}, {"command": 8, "file": 0, "line": 341, "parent": 24}, {"command": 8, "file": 0, "line": 341, "parent": 24}, {"command": 8, "file": 0, "line": 341, "parent": 24}, {"command": 8, "file": 0, "line": 341, "parent": 24}, {"command": 11, "file": 3, "line": 20, "parent": 21}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always"}, {"backtrace": 3, "fragment": "-ffunction-sections"}, {"backtrace": 3, "fragment": "-fdata-sections"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Werror=all"}, {"backtrace": 3, "fragment": "-Wno-error=unused-function"}, {"backtrace": 3, "fragment": "-Wno-error=unused-variable"}, {"backtrace": 3, "fragment": "-Wno-error=unused-but-set-variable"}, {"backtrace": 3, "fragment": "-Wno-error=deprecated-declarations"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wno-error=extra"}, {"backtrace": 3, "fragment": "-Wno-unused-parameter"}, {"backtrace": 3, "fragment": "-Wno-sign-compare"}, {"backtrace": 3, "fragment": "-Wno-enum-conversion"}, {"backtrace": 3, "fragment": "-gdwarf-4"}, {"backtrace": 3, "fragment": "-ggdb"}, {"backtrace": 3, "fragment": "-nostartfiles"}, {"backtrace": 3, "fragment": "-<PERSON><PERSON>"}, {"backtrace": 3, "fragment": "-freorder-blocks"}, {"backtrace": 3, "fragment": "-fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader/subproject=."}, {"backtrace": 3, "fragment": "-fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf=/IDF"}, {"backtrace": 3, "fragment": "-fstrict-volatile-bitfields"}, {"backtrace": 3, "fragment": "-fno-jump-tables"}, {"backtrace": 3, "fragment": "-fno-tree-switch-conversion"}, {"backtrace": 3, "fragment": "-fno-stack-protector"}, {"backtrace": 5, "fragment": "-std=gnu17"}, {"backtrace": 5, "fragment": "-Wno-old-style-declaration"}], "defines": [{"backtrace": 6, "define": "BOOTLOADER_BUILD=1"}, {"backtrace": 6, "define": "ESP_PLATFORM"}, {"backtrace": 6, "define": "IDF_VER=\"5.5.0\""}, {"backtrace": 6, "define": "NON_OS_BUILD=1"}, {"backtrace": 2, "define": "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE"}, {"backtrace": 2, "define": "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ"}, {"backtrace": 6, "define": "_GLIBCXX_HAVE_POSIX_SEMAPHORE"}, {"backtrace": 6, "define": "_GLIBCXX_USE_POSIX_SEMAPHORE"}, {"backtrace": 6, "define": "_GNU_SOURCE"}], "includes": [{"backtrace": 7, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/config"}, {"backtrace": 9, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc"}, {"backtrace": 11, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc/esp32c6"}, {"backtrace": 12, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/dma/include"}, {"backtrace": 13, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/ldo/include"}, {"backtrace": 14, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/debug_probe/include"}, {"backtrace": 15, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/include"}, {"backtrace": 16, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include"}, {"backtrace": 17, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/power_supply/include"}, {"backtrace": 19, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/include"}, {"backtrace": 20, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/esp_private"}, {"backtrace": 22, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/."}, {"backtrace": 22, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/include"}, {"backtrace": 22, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/private_include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/log/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/platform_include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/riscv/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/include"}, {"backtrace": 2, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/register"}, {"backtrace": 25, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/include"}, {"backtrace": 25, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/esp32c6/include"}, {"backtrace": 26, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash/include"}, {"backtrace": 26, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/platform_port/include"}, {"backtrace": 26, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/esp32c6/include"}, {"backtrace": 26, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/include"}, {"backtrace": 27, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/include"}, {"backtrace": 27, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/bootloader_flash/include"}, {"backtrace": 27, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/private_include"}, {"backtrace": 28, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_security/include"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "dependencies": [{"id": "__idf_esp_common::@5ba0aac035ae9b4e20a4"}], "id": "__idf_esp_hw_support::@ce4791f5a7554c569ddb", "name": "__idf_esp_hw_support", "nameOnDisk": "libesp_hw_support.a", "paths": {"build": "esp-idf/esp_hw_support", "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/cpu.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/esp_cpu_intr.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/esp_memory_utils.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/cpu_region_protect.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/rtc_clk_init.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/rtc_clk.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/pmu_param.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/pmu_init.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/pmu_sleep.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/rtc_time.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/chip_info.c", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/ocode_init.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}