{"backtrace": 6, "backtraceGraph": {"commands": ["add_custom_target", "include", "__build_process_project_includes", "idf_build_process", "project"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py/project_include.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/build.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 4, "file": 3, "line": 72, "parent": 0}, {"command": 3, "file": 2, "line": 740, "parent": 1}, {"command": 2, "file": 1, "line": 718, "parent": 2}, {"command": 1, "file": 1, "line": 467, "parent": 3}, {"file": 0, "parent": 4}, {"command": 0, "file": 0, "line": 189, "parent": 5}]}, "id": "erase_flash::@6890427a1f51a3e7e1df", "name": "erase_flash", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 6, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/CMakeFiles/erase_flash", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/CMakeFiles/erase_flash.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}