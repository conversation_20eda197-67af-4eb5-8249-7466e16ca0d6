sudo: false
language: python
os:
  - linux

git:
  depth: false

stages:
  - build

jobs:
  include:

    - name: "Arduino Build"
      if: tag IS blank AND (type = pull_request OR (type = push AND branch = master))
      stage: build
      script: bash $TRAVIS_BUILD_DIR/.github/scripts/on-push.sh

    - name: "PlatformIO Build"
      if: tag IS blank AND (type = pull_request OR (type = push AND branch = master))
      stage: build
      script: bash $TRAVIS_BUILD_DIR/.github/scripts/on-push.sh 1 1

notifications:
  email:
    on_success: change
    on_failure: change
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/60e65d0c78ea0a920347
    on_success: change  # options: [always|never|change] default: always
    on_failure: always  # options: [always|never|change] default: always
    on_start: false     # default: false
