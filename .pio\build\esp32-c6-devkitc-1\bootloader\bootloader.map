Archive member included to satisfy reference by file (symbol)

.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
                              (call_start_cpu0)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o) (bootloader_utility_load_partition_table)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (esp_partition_table_verify)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (bootloader_load_image)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (bootloader_sha256_start)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (bootloader_console_deinit)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (bootloader_ana_clock_glitch_reset_config)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o) (bootloader_init)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
                              (__assert_func)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (bootloader_common_ota_select_crc)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o) (bootloader_clock_configure)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o) (bootloader_init_mem)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o) (bootloader_fill_random)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (esp_flash_encryption_enabled)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (bootloader_random_disable)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (bootloader_mmap_get_free_pages)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o) (bootloader_flash_update_id)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o) (bootloader_clear_bss_section)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o) (bootloader_console_init)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o) (ESP_EFUSE_DIS_DIRECT_BOOT)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o) (esp_efuse_enable_rom_secure_download_mode)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o) (esp_efuse_read_field_blob)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o) (esp_efuse_utility_process)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o) (esp_efuse_get_key_dis_read)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o) (esp_efuse_utility_clear_program_registers)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o) (esp_bootloader_get_description)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o) (esp_cpu_configure_region_protection)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o) (rtc_clk_init)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o) (rtc_clk_32k_enable)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o) (get_act_hp_dbias)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o) (esp_rom_regi2c_write)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o) (wdt_hal_init)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o) (esp_log_timestamp)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o) (rv_utils_dbgr_is_attached)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o) (efuse_hal_chip_revision)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o) (efuse_hal_get_major_chip_version)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o) (lp_timer_hal_get_cycle_count)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (mmu_hal_unmap_all)
.pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o) (cache_hal_init)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o) (__lshrdi3)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o) (__ashldi3)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o) (__popcountsi2)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o) (__udivdi3)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                              C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o) (__clz_tab)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o) (memcmp)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o) (_impure_data)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o) (memset)
C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o) (memcpy)

Discarded input sections

 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .text.__getreent
                0x00000000        0xa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.bootloader_common_get_partition_description
                0x00000000       0x9e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.bootloader_atexit
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.bootloader_sha256_hex_to_str
                0x00000000       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.bootloader_sha256_flash_contents.str1.4
                0x00000000       0x2d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.bootloader_sha256_flash_contents
                0x00000000      0x100 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.esp_image_bootloader_offset_get
                0x00000000        0xa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .rodata.esp_image_bootloader_offset_set.str1.4
                0x00000000       0x44 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.esp_image_bootloader_offset_set
                0x00000000       0x3c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.bootloader_load_image_no_verify
                0x00000000        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.esp_image_verify
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.esp_image_get_metadata
                0x00000000       0xbe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.esp_image_verify_bootloader_data
                0x00000000       0x26 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.esp_image_verify_bootloader
                0x00000000       0x2c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.esp_image_get_flash_size
                0x00000000       0x52 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .text.esp_flash_write_protect_crypt_cnt
                0x00000000       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .text.esp_get_flash_encryption_mode
                0x00000000       0x8e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .rodata.esp_flash_encryption_set_release_mode.str1.4
                0x00000000       0xb6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .text.esp_flash_encryption_set_release_mode
                0x00000000      0x124 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .rodata.esp_flash_encryption_cfg_verify_release_mode.str1.4
                0x00000000      0x3c7 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .text.esp_flash_encryption_cfg_verify_release_mode
                0x00000000      0x334 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .text.bootloader_flash_erase_range
                0x00000000       0x7e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .text.bootloader_spi_flash_reset
                0x00000000       0x2c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .iram1.7       0x00000000       0x88 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .iram1.8       0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .text.bootloader_flash_update_size
                0x00000000        0xc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .iram1.1       0x00000000       0x2a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SYS_DATA_PART2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY5
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY4
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_USER_DATA_MAC_CUSTOM
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_USER_DATA
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_OCODE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_TEMP_CALIB
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_FLASH_VENDOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_FLASH_TEMP
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_FLASH_CAP
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_BLK_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_BLK_VERSION_MINOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_PKG_VERSION
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WAFER_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WAFER_VERSION_MINOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DBIAS_VOL_GAP
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DSLP_LP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DSLP_LP_DBG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_LSLP_HP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_LSLP_HP_DBG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ACTIVE_LP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ACTIVE_HP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .data.ESP_EFUSE_MAC_EXT
                0x00000000        0xc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .data.ESP_EFUSE_MAC
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SECURE_VERSION
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_FORCE_SEND_RESUME
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_UART_PRINT_CONTROL
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_DIRECT_BOOT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MODE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_FLASH_TPUW
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SECURE_BOOT_EN
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_CRYPT_DPA_ENABLE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SEC_DPA_LEVEL
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY_PURPOSE_5
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY_PURPOSE_4
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY_PURPOSE_3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY_PURPOSE_2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY_PURPOSE_1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_KEY_PURPOSE_0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WDT_DELAY_SEL
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_VDD_SPI_AS_GPIO
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_USB_EXCHG_PINS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_PAD_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SOFT_DIS_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_JTAG_SEL_ENABLE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_TWAI
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_USB_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_DIS_ICACHE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_SWAP_UART_SDIO_EN
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY5
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY4
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_RD_DIS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_VDD_SPI_AS_GPIO
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_USB_EXCHG_PINS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY5
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY4
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_CUSTOM_MAC
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_USR_DATA
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_OCODE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_TEMP_CALIB
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SYS_DATA_PART1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_VENDOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TEMP
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_CAP
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_PKG_VERSION
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DBIAS_VOL_GAP
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DSLP_LP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DSLP_LP_DBG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_LSLP_HP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_LSLP_HP_DBG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ACTIVE_LP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ACTIVE_HP_DBIAS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_MAC_EXT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_MAC
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_BLK1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_VERSION
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TPUW
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_EN
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SEC_DPA_LEVEL
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_5
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_4
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_3
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_WDT_DELAY_SEL
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_PAD_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_TWAI
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_JTAG
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_DIS_ICACHE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_SWAP_UART_SDIO_EN
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_CRYPT_DPA_ENABLE
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS_RD_DIS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .sdata.ESP_EFUSE_WR_DIS
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SYS_DATA_PART2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY5  0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY4  0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY3  0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY2  0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY1  0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY0  0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.USER_DATA_MAC_CUSTOM
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.USER_DATA
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_CAL_VOL_ATTEN2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_CAL_VOL_ATTEN1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.OCODE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.TEMP_CALIB
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.OPTIONAL_UNIQUE_ID
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.FLASH_VENDOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.FLASH_TEMP
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.FLASH_CAP
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.BLK_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.BLK_VERSION_MINOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.PKG_VERSION
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WAFER_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WAFER_VERSION_MINOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DBIAS_VOL_GAP
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DSLP_LP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DSLP_LP_DBG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.LSLP_HP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.LSLP_HP_DBG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ACTIVE_LP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ACTIVE_HP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.MAC_EXT
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .rodata.MAC    0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SECURE_VERSION
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.FORCE_SEND_RESUME
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.UART_PRINT_CONTROL
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_DIRECT_BOOT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_DOWNLOAD_MODE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.FLASH_TPUW
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SECURE_BOOT_EN
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.CRYPT_DPA_ENABLE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SEC_DPA_LEVEL
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY_PURPOSE_5
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY_PURPOSE_4
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY_PURPOSE_3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY_PURPOSE_2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY_PURPOSE_1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.KEY_PURPOSE_0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WDT_DELAY_SEL
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.VDD_SPI_AS_GPIO
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.USB_EXCHG_PINS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_PAD_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SOFT_DIS_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.JTAG_SEL_ENABLE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_TWAI
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_FORCE_DOWNLOAD
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_USB_SERIAL_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_USB_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.DIS_ICACHE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.SWAP_UART_SDIO_EN
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS_BLOCK_KEY5
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS_BLOCK_KEY4
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS_BLOCK_KEY3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS_BLOCK_KEY2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS_BLOCK_KEY1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS_BLOCK_KEY0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.RD_DIS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_VDD_SPI_AS_GPIO
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_USB_EXCHG_PINS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_KEY5
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_KEY4
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_KEY3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_KEY2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_KEY1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_KEY0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_CUSTOM_MAC
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLOCK_USR_DATA
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_OCODE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_TEMP_CALIB
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SYS_DATA_PART1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_FLASH_VENDOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_FLASH_TEMP
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_FLASH_CAP
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_PKG_VERSION
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DBIAS_VOL_GAP
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DSLP_LP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DSLP_LP_DBG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_LSLP_HP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_LSLP_HP_DBG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ACTIVE_LP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ACTIVE_HP_DBIAS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_MAC_EXT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_MAC
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_BLK1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SECURE_VERSION
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_FLASH_TPUW
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SECURE_BOOT_EN
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SEC_DPA_LEVEL
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_KEY_PURPOSE_5
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_KEY_PURPOSE_4
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_KEY_PURPOSE_3
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_KEY_PURPOSE_2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_KEY_PURPOSE_1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_KEY_PURPOSE_0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_WDT_DELAY_SEL
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_PAD_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_TWAI
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_USB_JTAG
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_DIS_ICACHE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_SWAP_UART_SDIO_EN
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_CRYPT_DPA_ENABLE
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS_RD_DIS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .srodata.WR_DIS
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .debug_info    0x00000000     0x258f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .debug_abbrev  0x00000000      0x106 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .debug_aranges
                0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .debug_line    0x00000000      0x28d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .debug_str     0x00000000     0x28aa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .comment       0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .riscv.attributes
                0x00000000       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .text.esp_efuse_get_pkg_ver
                0x00000000       0x2a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .text.esp_efuse_set_rom_log_scheme
                0x00000000       0x3e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .text.esp_efuse_disable_rom_download_mode
                0x00000000       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .text.esp_efuse_enable_rom_secure_download_mode
                0x00000000       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_info    0x00000000      0x3ba .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_abbrev  0x00000000      0x18f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_loc     0x00000000       0x1f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_aranges
                0x00000000       0x38 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_ranges  0x00000000       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_line    0x00000000      0x4a0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_str     0x00000000      0x629 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .comment       0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .debug_frame   0x00000000       0x80 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .riscv.attributes
                0x00000000       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_read_field_blob
                0x00000000       0x6e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.esp_efuse_read_field_bit.str1.4
                0x00000000       0x3b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_read_field_bit
                0x00000000       0x4a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_read_field_cnt
                0x00000000       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_write_field_blob
                0x00000000       0x74 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.esp_efuse_write_field_cnt.str1.4
                0x00000000       0x4e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_write_field_cnt
                0x00000000       0xae .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_write_field_bit
                0x00000000       0x5a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_get_field_size
                0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_write_reg
                0x00000000       0x5a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_read_block
                0x00000000       0x44 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_read_reg
                0x00000000       0x46 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_write_block
                0x00000000       0x44 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.esp_efuse_batch_write_begin.str1.4
                0x00000000       0x51 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_batch_write_begin
                0x00000000       0x72 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.esp_efuse_batch_write_cancel.str1.4
                0x00000000       0x5f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_batch_write_cancel
                0x00000000       0x72 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.esp_efuse_batch_write_commit.str1.4
                0x00000000       0x37 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_batch_write_commit
                0x00000000       0x8e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_check_errors
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.esp_efuse_destroy_block.str1.4
                0x00000000      0x120 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text.esp_efuse_destroy_block
                0x00000000      0x13c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.__func__.0
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.__func__.1
                0x00000000       0x13 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .rodata.__func__.2
                0x00000000       0x19 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .sbss.s_batch_writing_mode
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_info    0x00000000     0x1295 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_abbrev  0x00000000      0x3f5 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_loc     0x00000000      0xab2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_aranges
                0x00000000       0x98 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_ranges  0x00000000       0xe0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_line    0x00000000     0x1435 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_str     0x00000000      0x9c0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .comment       0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .debug_frame   0x00000000      0x220 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .riscv.attributes
                0x00000000       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.write_reg.str1.4
                0x00000000       0xb4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.write_reg
                0x00000000       0x7a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_process.str1.4
                0x00000000       0x5d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_process
                0x00000000      0x174 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_reset
                0x00000000       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_burn_efuses
                0x00000000       0x2e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_erase_virt_blocks
                0x00000000        0x2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_update_virt_blocks.str1.4
                0x00000000       0x27 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_update_virt_blocks
                0x00000000       0x2a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_debug_dump_single_block.str1.4
                0x00000000       0x12 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_debug_dump_single_block
                0x00000000       0x8e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_debug_dump_pending
                0x00000000       0x46 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_debug_dump_blocks.str1.4
                0x00000000        0xd .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_debug_dump_blocks
                0x00000000       0x46 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_get_number_of_items
                0x00000000       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_read_reg
                0x00000000       0x68 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_fill_buff
                0x00000000       0xde .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_count_once
                0x00000000       0x62 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_write_cnt.str1.4
                0x00000000       0x31 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_write_cnt
                0x00000000       0xd4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_write_reg.str1.4
                0x00000000       0x53 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_write_reg
                0x00000000       0x64 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_write_blob
                0x00000000       0x92 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_get_read_register_address.str1.4
                0x00000000       0x16 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_get_read_register_address
                0x00000000       0x3e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_is_correct_written_data.str1.4
                0x00000000       0xba .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_is_correct_written_data
                0x00000000       0xd6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.__func__.0
                0x00000000       0x2c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.__func__.1
                0x00000000       0x1b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.__func__.2
                0x00000000        0xa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.__func__.3
                0x00000000        0xf .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.__func__.4
                0x00000000       0x1a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .sbss.s_burn_counter
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_info    0x00000000     0x1456 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_abbrev  0x00000000      0x458 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_loc     0x00000000     0x1331 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_aranges
                0x00000000       0xa8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_ranges  0x00000000      0x2d0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_line    0x00000000     0x1943 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_str     0x00000000      0xb23 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .comment       0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_frame   0x00000000      0x2bc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .riscv.attributes
                0x00000000       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_block_is_empty
                0x00000000       0x38 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_write_protect
                0x00000000       0x6a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_read_protect
                0x00000000       0x3e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_coding_scheme
                0x00000000        0x6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_purpose_field
                0x00000000       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_key
                0x00000000       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.esp_efuse_get_key_dis_read.str1.4
                0x00000000       0x8f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_key_dis_read
                0x00000000       0x4a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_key_dis_read
                0x00000000       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_key_dis_write
                0x00000000       0x4a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_key_dis_write
                0x00000000       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_key_purpose
                0x00000000       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_key_purpose
                0x00000000       0x3c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_keypurpose_dis_write
                0x00000000       0x4a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_keypurpose_dis_write
                0x00000000       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_find_purpose
                0x00000000       0x42 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_key_block_unused
                0x00000000       0x5e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_find_unused_key_block
                0x00000000       0x2a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_count_unused_key_blocks
                0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.esp_efuse_write_key.str1.4
                0x00000000       0x65 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_write_key
                0x00000000      0x114 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.esp_efuse_write_keys.str1.4
                0x00000000       0xd2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_write_keys
                0x00000000      0x156 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.esp_efuse_get_digest_revoke.str1.4
                0x00000000       0x42 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_digest_revoke
                0x00000000       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_digest_revoke
                0x00000000       0x26 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_get_write_protect_of_digest_revoke
                0x00000000       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_efuse_set_write_protect_of_digest_revoke
                0x00000000       0x26 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.esp_secure_boot_read_key_digests.str1.4
                0x00000000       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text.esp_secure_boot_read_key_digests
                0x00000000       0xa4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.__func__.0
                0x00000000       0x21 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.__func__.1
                0x00000000       0x2d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.__func__.2
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.__func__.3
                0x00000000       0x23 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.__func__.4
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.__func__.5
                0x00000000       0x1b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.s_revoke_table
                0x00000000       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .rodata.s_table
                0x00000000       0x78 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_info    0x00000000     0x1677 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_abbrev  0x00000000      0x3e2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_loc     0x00000000      0xdf1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_aranges
                0x00000000       0xe0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_ranges  0x00000000      0x208 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_line    0x00000000     0x15f2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_str     0x00000000     0x1118 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .comment       0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .debug_frame   0x00000000      0x324 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .riscv.attributes
                0x00000000       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_clear_program_registers
                0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_check_errors
                0x00000000        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_burn_chip_opt.str1.4
                0x00000000      0x1b0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_burn_chip_opt
                0x00000000      0x2ba .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_burn_chip
                0x00000000        0xc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.esp_efuse_utility_apply_new_coding_scheme.str1.4
                0x00000000       0x3f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text.esp_efuse_utility_apply_new_coding_scheme
                0x00000000       0xb4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.range_write_addr_blocks
                0x00000000       0x58 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .bss.write_mass_blocks
                0x00000000      0x160 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .rodata.range_read_addr_blocks
                0x00000000       0x58 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_info    0x00000000      0x9b0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_abbrev  0x00000000      0x2c8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_loc     0x00000000      0x38c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_aranges
                0x00000000       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_ranges  0x00000000      0x118 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_line    0x00000000      0xdb5 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_str     0x00000000      0x877 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .comment       0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .debug_frame   0x00000000       0xdc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .riscv.attributes
                0x00000000       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_bbpll_add_consumer
                0x00000000       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_bbpll_remove_consumer
                0x00000000       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_32k_disable_external
                0x00000000       0x2c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_32k_bootstrap
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_32k_enabled
                0x00000000        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_8m_enabled
                0x00000000        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_freq_to_pll_and_pll_lock_release
                0x00000000       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_freq_set_config_fast
                0x00000000       0x44 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_set_to_default_config
                0x00000000       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_freq_set_xtal
                0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_freq_set_xtal_for_sleep
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_dig_clk8m_enable
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_dig_clk8m_disable
                0x00000000       0x1a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_dig_8m_enabled
                0x00000000        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.pmu_hp_system_power_param_default.str1.4
                0x00000000       0x55 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text.pmu_hp_system_power_param_default
                0x00000000       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.pmu_hp_system_clock_param_default.str1.4
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text.pmu_hp_system_clock_param_default
                0x00000000       0x3c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.pmu_hp_system_digital_param_default.str1.4
                0x00000000       0x1e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text.pmu_hp_system_digital_param_default
                0x00000000       0x3c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.pmu_hp_system_analog_param_default.str1.4
                0x00000000       0x1d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text.pmu_hp_system_analog_param_default
                0x00000000       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.pmu_hp_system_retention_param_default.str1.4
                0x00000000       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text.pmu_hp_system_retention_param_default
                0x00000000       0x3c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.pmu_lp_system_power_param_default.str1.4
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text.pmu_lp_system_power_param_default
                0x00000000       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.pmu_lp_system_analog_param_default.str1.4
                0x00000000       0x1d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text.pmu_lp_system_analog_param_default
                0x00000000       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.lp_analog.0
                0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.__func__.1
                0x00000000       0x23 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.lp_power.2
                0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.__func__.3
                0x00000000       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.hp_retention.4
                0x00000000       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.__func__.5
                0x00000000       0x26 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.hp_analog.6
                0x00000000       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.__func__.7
                0x00000000       0x23 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.hp_digital.8
                0x00000000        0xc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.__func__.9
                0x00000000       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.hp_clock.10
                0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.__func__.11
                0x00000000       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.hp_power.12
                0x00000000       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .rodata.__func__.13
                0x00000000       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .iram1.1       0x00000000       0x4e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .iram1.2       0x00000000       0x9c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .rodata.__func__.1
                0x00000000       0x16 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .text.wdt_hal_deinit
                0x00000000       0x84 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text.efuse_hal_get_mac
                0x00000000       0x14 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .iram1.3       0x00000000       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text.efuse_hal_set_timing
                0x00000000       0x44 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text.efuse_hal_read
                0x00000000       0x4c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text.efuse_hal_clear_program_registers
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text.efuse_hal_program
                0x00000000       0x6a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text.efuse_hal_rs_calculate
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text.efuse_hal_is_coding_error_in_block
                0x00000000       0x54 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .iram1.1       0x00000000       0x46 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .iram1.3       0x00000000       0x14 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .iram1.4       0x00000000       0x14 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .text.mmu_hal_bytes_to_pages
                0x00000000       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .text.mmu_hal_paddr_to_vaddr
                0x00000000       0xe8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .text.mmu_hal_unmap_region
                0x00000000       0xa6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .text.mmu_hal_vaddr_to_paddr
                0x00000000       0xde .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .text          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .data          0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .bss           0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.s_get_cache_state
                0x00000000       0x3e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_suspend
                0x00000000       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_resume
                0x00000000       0x38 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_is_cache_enabled
                0x00000000        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_vaddr_to_cache_level_id
                0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_invalidate_addr
                0x00000000       0x3c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_freeze
                0x00000000       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_unfreeze
                0x00000000       0x1a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text.cache_hal_get_cache_line_size
                0x00000000       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 .text          0x00000000       0x28 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_info    0x00000000      0x1b6 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_abbrev  0x00000000      0x10c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_loclists
                0x00000000       0x6b C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_aranges
                0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line    0x00000000      0x107 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_str     0x00000000      0x1e4 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line_str
                0x00000000      0x1b2 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_frame   0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .text          0x00000000       0x28 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_info    0x00000000      0x1b6 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_abbrev  0x00000000      0x10c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_loclists
                0x00000000       0x6b C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_aranges
                0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line    0x00000000      0x107 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_str     0x00000000      0x1e4 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line_str
                0x00000000      0x1b2 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_frame   0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .text          0x00000000       0x42 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_info    0x00000000       0xe6 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_abbrev  0x00000000       0x65 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_loclists
                0x00000000       0xd6 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_aranges
                0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line    0x00000000       0xe9 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_str     0x00000000      0x1ab C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line_str
                0x00000000      0x1b2 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_frame   0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .text          0x00000000      0x35a C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_info    0x00000000      0x776 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x00000000      0x1a6 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_loclists
                0x00000000      0x6ad C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_rnglists
                0x00000000       0x8f C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line    0x00000000      0x97b C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_str     0x00000000      0x25a C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line_str
                0x00000000      0x1b2 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .eh_frame      0x00000000       0x28 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .text          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .rodata        0x00000000      0x100 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_info    0x00000000       0xed C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_abbrev  0x00000000       0x70 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_aranges
                0x00000000       0x18 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line    0x00000000       0x3f C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_str     0x00000000      0x1a7 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line_str
                0x00000000      0x1b2 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .text          0x00000000       0x48 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_info    0x00000000      0x10f C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_abbrev  0x00000000       0x8a C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_loclists
                0x00000000      0x130 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_aranges
                0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line    0x00000000      0x157 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_str     0x00000000      0x113 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line_str
                0x00000000      0x273 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_frame   0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .text          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .data          0x00000000       0xf0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .sdata         0x00000000        0x4 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_info    0x00000000      0x84c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_abbrev  0x00000000      0x174 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_aranges
                0x00000000       0x18 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line    0x00000000       0x51 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_str     0x00000000      0x4e3 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line_str
                0x00000000      0x27b C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .text          0x00000000       0xa8 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line    0x00000000      0x18e C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line_str
                0x00000000       0xdc C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_info    0x00000000       0x33 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_abbrev  0x00000000       0x28 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_aranges
                0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_str     0x00000000       0xf1 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .riscv.attributes
                0x00000000       0x5a C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .text          0x00000000       0xe8 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .data          0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .bss           0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_info    0x00000000      0x256 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_abbrev  0x00000000      0x107 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_loclists
                0x00000000      0x1d6 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_aranges
                0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line    0x00000000      0x31c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_str     0x00000000      0x134 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line_str
                0x00000000      0x340 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .comment       0x00000000       0x30 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .note.GNU-stack
                0x00000000        0x0 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_frame   0x00000000       0x20 C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .riscv.attributes
                0x00000000       0x5c C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)

Memory Configuration

Name             Origin             Length             Attributes
iram_seg         0x4086b910         0x00002d00         xrw
iram_loader_seg  0x4086e610         0x00007000         xrw
dram_seg         0x40875610         0x00005000         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

                0x00000000                        IDF_TARGET_ESP32C6 = 0x0
START GROUP
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_common\libesp_common.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_system\libesp_system.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\micro-ecc\libmicro-ecc.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\soc\libsoc.a
LOAD .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\spi_flash\libspi_flash.a
END GROUP
LOAD C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a
LOAD C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libnosys.a
LOAD C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a
                0x40000018                        rtc_get_reset_reason = 0x40000018
                0x4000001c                        analog_super_wdt_reset_happened = 0x4000001c
                0x40000020                        rtc_get_wakeup_cause = 0x40000020
                0x40000024                        rtc_unhold_all_pads = 0x40000024
                0x40000028                        ets_printf = 0x40000028
                0x4000002c                        ets_install_putc1 = 0x4000002c
                0x40000030                        ets_install_putc2 = 0x40000030
                0x40000034                        ets_install_uart_printf = 0x40000034
                0x40000038                        ets_install_usb_printf = 0x40000038
                0x4000003c                        ets_get_printf_channel = 0x4000003c
                0x40000040                        ets_delay_us = 0x40000040
                0x40000044                        ets_get_cpu_frequency = 0x40000044
                0x40000048                        ets_update_cpu_frequency = 0x40000048
                0x4000004c                        ets_install_lock = 0x4000004c
                0x40000050                        UartRxString = 0x40000050
                0x40000054                        UartGetCmdLn = 0x40000054
                0x40000058                        uart_tx_one_char = 0x40000058
                0x4000005c                        uart_tx_one_char2 = 0x4000005c
                0x40000060                        uart_rx_one_char = 0x40000060
                0x40000064                        uart_rx_one_char_block = 0x40000064
                0x40000068                        uart_rx_intr_handler = 0x40000068
                0x4000006c                        uart_rx_readbuff = 0x4000006c
                0x40000070                        uartAttach = 0x40000070
                0x40000074                        uart_tx_flush = 0x40000074
                0x40000078                        uart_tx_wait_idle = 0x40000078
                0x4000007c                        uart_div_modify = 0x4000007c
                0x40000080                        ets_write_char_uart = 0x40000080
                0x40000084                        uart_tx_switch = 0x40000084
                0x40000088                        roundup2 = 0x40000088
                0x4000008c                        multofup = 0x4000008c
                0x40000090                        software_reset = 0x40000090
                0x40000094                        software_reset_cpu = 0x40000094
                0x40000098                        ets_clk_assist_debug_clock_enable = 0x40000098
                0x4000009c                        clear_super_wdt_reset_flag = 0x4000009c
                0x400000a0                        disable_default_watchdog = 0x400000a0
                0x400000a4                        esp_rom_set_rtc_wake_addr = 0x400000a4
                0x400000a8                        esp_rom_get_rtc_wake_addr = 0x400000a8
                0x400000ac                        send_packet = 0x400000ac
                0x400000b0                        recv_packet = 0x400000b0
                0x400000b4                        GetUartDevice = 0x400000b4
                0x400000b8                        UartDwnLdProc = 0x400000b8
                0x400000bc                        GetSecurityInfoProc = 0x400000bc
                0x400000c0                        Uart_Init = 0x400000c0
                0x400000c4                        ets_set_user_start = 0x400000c4
                0x4004fffc                        ets_rom_layout_p = 0x4004fffc
                0x4087fff8                        ets_ops_table_ptr = 0x4087fff8
                0x4087fffc                        g_saved_pc = 0x4087fffc
                0x400000c8                        mz_adler32 = 0x400000c8
                0x400000cc                        mz_free = 0x400000cc
                0x400000d0                        tdefl_compress = 0x400000d0
                0x400000d4                        tdefl_compress_buffer = 0x400000d4
                0x400000d8                        tdefl_compress_mem_to_heap = 0x400000d8
                0x400000dc                        tdefl_compress_mem_to_mem = 0x400000dc
                0x400000e0                        tdefl_compress_mem_to_output = 0x400000e0
                0x400000e4                        tdefl_get_adler32 = 0x400000e4
                0x400000e8                        tdefl_get_prev_return_status = 0x400000e8
                0x400000ec                        tdefl_init = 0x400000ec
                0x400000f0                        tdefl_write_image_to_png_file_in_memory = 0x400000f0
                0x400000f4                        tdefl_write_image_to_png_file_in_memory_ex = 0x400000f4
                0x400000f8                        tinfl_decompress = 0x400000f8
                0x400000fc                        tinfl_decompress_mem_to_callback = 0x400000fc
                0x40000100                        tinfl_decompress_mem_to_heap = 0x40000100
                0x40000104                        tinfl_decompress_mem_to_mem = 0x40000104
                0x40000108                        jd_prepare = 0x40000108
                0x4000010c                        jd_decomp = 0x4000010c
                0x40000110                        esp_rom_spiflash_wait_idle = 0x40000110
                0x40000114                        esp_rom_spiflash_write_encrypted = 0x40000114
                0x40000118                        esp_rom_spiflash_write_encrypted_dest = 0x40000118
                0x4000011c                        esp_rom_spiflash_write_encrypted_enable = 0x4000011c
                0x40000120                        esp_rom_spiflash_write_encrypted_disable = 0x40000120
                0x40000124                        esp_rom_spiflash_erase_chip = 0x40000124
                0x40000128                        _esp_rom_spiflash_erase_sector = 0x40000128
                0x4000012c                        _esp_rom_spiflash_erase_block = 0x4000012c
                0x40000130                        _esp_rom_spiflash_write = 0x40000130
                0x40000134                        _esp_rom_spiflash_read = 0x40000134
                0x40000138                        _esp_rom_spiflash_unlock = 0x40000138
                0x4000013c                        _SPIEraseArea = 0x4000013c
                0x40000140                        _SPI_write_enable = 0x40000140
                0x40000144                        esp_rom_spiflash_erase_sector = 0x40000144
                0x40000148                        esp_rom_spiflash_erase_block = 0x40000148
                0x4000014c                        esp_rom_spiflash_write = 0x4000014c
                0x40000150                        esp_rom_spiflash_read = 0x40000150
                0x40000154                        esp_rom_spiflash_unlock = 0x40000154
                0x40000158                        SPIEraseArea = 0x40000158
                0x4000015c                        SPI_write_enable = 0x4000015c
                0x40000160                        esp_rom_spiflash_config_param = 0x40000160
                0x40000164                        esp_rom_spiflash_read_user_cmd = 0x40000164
                0x40000168                        esp_rom_spiflash_select_qio_pins = 0x40000168
                0x4000016c                        esp_rom_spi_flash_auto_sus_res = 0x4000016c
                0x40000170                        esp_rom_spi_flash_send_resume = 0x40000170
                0x40000174                        esp_rom_spi_flash_update_id = 0x40000174
                0x40000178                        esp_rom_spiflash_config_clk = 0x40000178
                0x4000017c                        esp_rom_spiflash_config_readmode = 0x4000017c
                0x40000180                        esp_rom_spiflash_read_status = 0x40000180
                0x40000184                        esp_rom_spiflash_read_statushigh = 0x40000184
                0x40000188                        esp_rom_spiflash_write_status = 0x40000188
                0x4000018c                        spi_cache_mode_switch = 0x4000018c
                0x40000190                        spi_common_set_dummy_output = 0x40000190
                0x40000194                        spi_common_set_flash_cs_timing = 0x40000194
                0x40000198                        esp_rom_spi_set_address_bit_len = 0x40000198
                0x4000019c                        SPILock = 0x4000019c
                0x400001a0                        SPIMasterReadModeCnfig = 0x400001a0
                0x400001a4                        SPI_Common_Command = 0x400001a4
                0x400001a8                        SPI_WakeUp = 0x400001a8
                0x400001ac                        SPI_block_erase = 0x400001ac
                0x400001b0                        SPI_chip_erase = 0x400001b0
                0x400001b4                        SPI_init = 0x400001b4
                0x400001b8                        SPI_page_program = 0x400001b8
                0x400001bc                        SPI_read_data = 0x400001bc
                0x400001c0                        SPI_sector_erase = 0x400001c0
                0x400001c4                        SelectSpiFunction = 0x400001c4
                0x400001c8                        SetSpiDrvs = 0x400001c8
                0x400001cc                        Wait_SPI_Idle = 0x400001cc
                0x400001d0                        spi_dummy_len_fix = 0x400001d0
                0x400001d4                        Disable_QMode = 0x400001d4
                0x400001d8                        Enable_QMode = 0x400001d8
                0x400001dc                        spi_flash_attach = 0x400001dc
                0x400001e0                        spi_flash_get_chip_size = 0x400001e0
                0x400001e4                        spi_flash_guard_set = 0x400001e4
                0x400001e8                        spi_flash_guard_get = 0x400001e8
                0x400001ec                        spi_flash_read_encrypted = 0x400001ec
                0x4087fff0                        rom_spiflash_legacy_funcs = 0x4087fff0
                0x4087ffec                        rom_spiflash_legacy_data = 0x4087ffec
                0x4087fff4                        g_flash_guard_ops = 0x4087fff4
                0x40000278                        esp_rom_spiflash_write_disable = 0x40000278
                0x40000628                        Cache_Get_ICache_Line_Size = 0x40000628
                0x4000062c                        Cache_Get_Mode = 0x4000062c
                0x40000630                        Cache_Address_Through_Cache = 0x40000630
                0x40000634                        ROM_Boot_Cache_Init = 0x40000634
                0x40000638                        MMU_Set_Page_Mode = 0x40000638
                0x4000063c                        MMU_Get_Page_Mode = 0x4000063c
                0x40000640                        Cache_Invalidate_ICache_Items = 0x40000640
                0x40000644                        Cache_Op_Addr = 0x40000644
                0x40000648                        Cache_Invalidate_Addr = 0x40000648
                0x4000064c                        Cache_Invalidate_ICache_All = 0x4000064c
                0x40000650                        Cache_Mask_All = 0x40000650
                0x40000654                        Cache_UnMask_Dram0 = 0x40000654
                0x40000658                        Cache_Suspend_ICache_Autoload = 0x40000658
                0x4000065c                        Cache_Resume_ICache_Autoload = 0x4000065c
                0x40000660                        Cache_Start_ICache_Preload = 0x40000660
                0x40000664                        Cache_ICache_Preload_Done = 0x40000664
                0x40000668                        Cache_End_ICache_Preload = 0x40000668
                0x4000066c                        Cache_Config_ICache_Autoload = 0x4000066c
                0x40000670                        Cache_Enable_ICache_Autoload = 0x40000670
                0x40000674                        Cache_Disable_ICache_Autoload = 0x40000674
                0x40000678                        Cache_Enable_ICache_PreLock = 0x40000678
                0x4000067c                        Cache_Disable_ICache_PreLock = 0x4000067c
                0x40000680                        Cache_Lock_ICache_Items = 0x40000680
                0x40000684                        Cache_Unlock_ICache_Items = 0x40000684
                0x40000688                        Cache_Lock_Addr = 0x40000688
                0x4000068c                        Cache_Unlock_Addr = 0x4000068c
                0x40000690                        Cache_Disable_ICache = 0x40000690
                0x40000694                        Cache_Enable_ICache = 0x40000694
                0x40000698                        Cache_Suspend_ICache = 0x40000698
                0x4000069c                        Cache_Resume_ICache = 0x4000069c
                0x400006a0                        Cache_Freeze_ICache_Enable = 0x400006a0
                0x400006a4                        Cache_Freeze_ICache_Disable = 0x400006a4
                0x400006a8                        Cache_Set_IDROM_MMU_Size = 0x400006a8
                0x400006ac                        Cache_Get_IROM_MMU_End = 0x400006ac
                0x400006b0                        Cache_Get_DROM_MMU_End = 0x400006b0
                0x400006b4                        Cache_MMU_Init = 0x400006b4
                0x400006b8                        Cache_MSPI_MMU_Set = 0x400006b8
                0x400006bc                        Cache_Travel_Tag_Memory = 0x400006bc
                0x400006c0                        Cache_Get_Virtual_Addr = 0x400006c0
                0x4087ffcc                        rom_cache_op_cb = 0x4087ffcc
                0x4087ffc8                        rom_cache_internal_table_ptr = 0x4087ffc8
                0x400006c4                        ets_clk_get_xtal_freq = 0x400006c4
                0x400006c8                        ets_clk_get_cpu_freq = 0x400006c8
                0x400006cc                        ets_clk_apb_wait_ready = 0x400006cc
                0x400006d0                        ets_clk_mspi_apb_wait_ready = 0x400006d0
                0x400006d4                        gpio_input_get = 0x400006d4
                0x400006d8                        gpio_matrix_in = 0x400006d8
                0x400006dc                        gpio_matrix_out = 0x400006dc
                0x400006e8                        gpio_output_set = 0x400006e8
                0x400006ec                        gpio_pad_hold = 0x400006ec
                0x400006f0                        gpio_pad_input_disable = 0x400006f0
                0x400006f4                        gpio_pad_input_enable = 0x400006f4
                0x400006f8                        gpio_pad_pulldown = 0x400006f8
                0x400006fc                        gpio_pad_pullup = 0x400006fc
                0x40000700                        gpio_pad_select_gpio = 0x40000700
                0x40000704                        gpio_pad_set_drv = 0x40000704
                0x40000708                        gpio_pad_unhold = 0x40000708
                0x4000070c                        gpio_pin_wakeup_disable = 0x4000070c
                0x40000710                        gpio_pin_wakeup_enable = 0x40000710
                0x40000714                        gpio_bypass_matrix_in = 0x40000714
                [!provide]                        PROVIDE (esprv_intc_int_set_priority = 0x40000718)
                [!provide]                        PROVIDE (esprv_intc_int_set_threshold = 0x4000071c)
                [!provide]                        PROVIDE (esprv_intc_int_enable = 0x40000720)
                [!provide]                        PROVIDE (esprv_intc_int_disable = 0x40000724)
                [!provide]                        PROVIDE (esprv_intc_int_set_type = 0x40000728)
                [!provide]                        PROVIDE (intr_handler_set = 0x4000072c)
                0x40000730                        intr_matrix_set = 0x40000730
                0x40000734                        ets_intr_lock = 0x40000734
                0x40000738                        ets_intr_unlock = 0x40000738
                0x4000073c                        ets_isr_attach = 0x4000073c
                0x40000740                        ets_isr_mask = 0x40000740
                0x40000744                        ets_isr_unmask = 0x40000744
                0x40000748                        md5_vector = 0x40000748
                0x4000074c                        MD5Init = 0x4000074c
                0x40000750                        MD5Update = 0x40000750
                0x40000754                        MD5Final = 0x40000754
                0x40000758                        crc32_le = 0x40000758
                0x4000075c                        crc16_le = 0x4000075c
                0x40000760                        crc8_le = 0x40000760
                0x40000764                        crc32_be = 0x40000764
                0x40000768                        crc16_be = 0x40000768
                0x4000076c                        crc8_be = 0x4000076c
                0x40000770                        esp_crc8 = 0x40000770
                0x40000774                        ets_sha_enable = 0x40000774
                0x40000778                        ets_sha_disable = 0x40000778
                0x4000077c                        ets_sha_get_state = 0x4000077c
                0x40000780                        ets_sha_init = 0x40000780
                0x40000784                        ets_sha_process = 0x40000784
                0x40000788                        ets_sha_starts = 0x40000788
                0x4000078c                        ets_sha_update = 0x4000078c
                0x40000790                        ets_sha_finish = 0x40000790
                0x40000794                        ets_sha_clone = 0x40000794
                0x40000798                        ets_hmac_enable = 0x40000798
                0x4000079c                        ets_hmac_disable = 0x4000079c
                0x400007a0                        ets_hmac_calculate_message = 0x400007a0
                0x400007a4                        ets_hmac_calculate_downstream = 0x400007a4
                0x400007a8                        ets_hmac_invalidate_downstream = 0x400007a8
                0x400007ac                        ets_jtag_enable_temporarily = 0x400007ac
                0x400007b0                        ets_aes_enable = 0x400007b0
                0x400007b4                        ets_aes_disable = 0x400007b4
                0x400007b8                        ets_aes_setkey = 0x400007b8
                0x400007bc                        ets_aes_block = 0x400007bc
                0x400007c0                        ets_aes_setkey_dec = 0x400007c0
                0x400007c4                        ets_aes_setkey_enc = 0x400007c4
                0x400007c8                        ets_bigint_enable = 0x400007c8
                0x400007cc                        ets_bigint_disable = 0x400007cc
                0x400007d0                        ets_bigint_multiply = 0x400007d0
                0x400007d4                        ets_bigint_modmult = 0x400007d4
                0x400007d8                        ets_bigint_modexp = 0x400007d8
                0x400007dc                        ets_bigint_wait_finish = 0x400007dc
                0x400007e0                        ets_bigint_getz = 0x400007e0
                0x400007e4                        ets_ds_enable = 0x400007e4
                0x400007e8                        ets_ds_disable = 0x400007e8
                0x400007ec                        ets_ds_start_sign = 0x400007ec
                0x400007f0                        ets_ds_is_busy = 0x400007f0
                0x400007f4                        ets_ds_finish_sign = 0x400007f4
                0x400007f8                        ets_ds_encrypt_params = 0x400007f8
                0x400007fc                        ets_mgf1_sha256 = 0x400007fc
                0x4004fff8                        crc32_le_table_ptr = 0x4004fff8
                0x4004fff4                        crc16_le_table_ptr = 0x4004fff4
                0x4004fff0                        crc8_le_table_ptr = 0x4004fff0
                0x4004ffec                        crc32_be_table_ptr = 0x4004ffec
                0x4004ffe8                        crc16_be_table_ptr = 0x4004ffe8
                0x4004ffe4                        crc8_be_table_ptr = 0x4004ffe4
                0x40000800                        ets_efuse_read = 0x40000800
                0x40000804                        ets_efuse_program = 0x40000804
                0x40000808                        ets_efuse_clear_program_registers = 0x40000808
                0x4000080c                        ets_efuse_write_key = 0x4000080c
                0x40000810                        ets_efuse_get_read_register_address = 0x40000810
                0x40000814                        ets_efuse_get_key_purpose = 0x40000814
                0x40000818                        ets_efuse_key_block_unused = 0x40000818
                0x4000081c                        ets_efuse_find_unused_key_block = 0x4000081c
                0x40000820                        ets_efuse_rs_calculate = 0x40000820
                0x40000824                        ets_efuse_count_unused_key_blocks = 0x40000824
                0x40000828                        ets_efuse_secure_boot_enabled = 0x40000828
                0x4000082c                        ets_efuse_secure_boot_aggressive_revoke_enabled = 0x4000082c
                0x40000830                        ets_efuse_cache_encryption_enabled = 0x40000830
                0x40000834                        ets_efuse_download_modes_disabled = 0x40000834
                0x40000838                        ets_efuse_find_purpose = 0x40000838
                0x4000083c                        ets_efuse_force_send_resume = 0x4000083c
                0x40000840                        ets_efuse_get_flash_delay_us = 0x40000840
                0x40000844                        ets_efuse_get_mac = 0x40000844
                0x40000848                        ets_efuse_get_uart_print_control = 0x40000848
                0x4000084c                        ets_efuse_direct_boot_mode_disabled = 0x4000084c
                0x40000850                        ets_efuse_security_download_modes_enabled = 0x40000850
                0x40000854                        ets_efuse_set_timing = 0x40000854
                0x40000858                        ets_efuse_jtag_disabled = 0x40000858
                0x4000085c                        ets_efuse_usb_print_is_disabled = 0x4000085c
                0x40000860                        ets_efuse_usb_download_mode_disabled = 0x40000860
                0x40000864                        ets_efuse_usb_device_disabled = 0x40000864
                0x40000868                        ets_efuse_secure_boot_fast_wake_enabled = 0x40000868
                0x4000086c                        ets_emsa_pss_verify = 0x4000086c
                0x40000870                        ets_rsa_pss_verify = 0x40000870
                0x40000874                        ets_secure_boot_verify_bootloader_with_keys = 0x40000874
                0x40000878                        ets_secure_boot_verify_signature = 0x40000878
                0x4000087c                        ets_secure_boot_read_key_digests = 0x4000087c
                0x40000880                        ets_secure_boot_revoke_public_key_digest = 0x40000880
                0x40000a80                        usb_serial_device_rx_one_char = 0x40000a80
                0x40000a84                        usb_serial_device_rx_one_char_block = 0x40000a84
                0x40000a88                        usb_serial_device_tx_flush = 0x40000a88
                0x40000a8c                        usb_serial_device_tx_one_char = 0x40000a8c
                0x40000a90                        lldesc_build_chain = 0x40000a90
                0x40000a94                        sip_after_tx_complete = 0x40000a94
                0x40000a98                        sip_alloc_to_host_evt = 0x40000a98
                0x40000a9c                        sip_download_begin = 0x40000a9c
                0x40000aa0                        sip_get_ptr = 0x40000aa0
                0x40000aa4                        sip_get_state = 0x40000aa4
                0x40000aa8                        sip_init_attach = 0x40000aa8
                0x40000aac                        sip_install_rx_ctrl_cb = 0x40000aac
                0x40000ab0                        sip_install_rx_data_cb = 0x40000ab0
                0x40000ab4                        sip_is_active = 0x40000ab4
                0x40000ab8                        sip_post_init = 0x40000ab8
                0x40000abc                        sip_reclaim_from_host_cmd = 0x40000abc
                0x40000ac0                        sip_reclaim_tx_data_pkt = 0x40000ac0
                0x40000ac4                        sip_send = 0x40000ac4
                0x40000ac8                        sip_to_host_chain_append = 0x40000ac8
                0x40000acc                        sip_to_host_evt_send_done = 0x40000acc
                0x40000ad0                        slc_add_credits = 0x40000ad0
                0x40000ad4                        slc_enable = 0x40000ad4
                0x40000ad8                        slc_from_host_chain_fetch = 0x40000ad8
                0x40000adc                        slc_from_host_chain_recycle = 0x40000adc
                0x40000ae0                        slc_has_pkt_to_host = 0x40000ae0
                0x40000ae4                        slc_init_attach = 0x40000ae4
                0x40000ae8                        slc_init_credit = 0x40000ae8
                0x40000aec                        slc_reattach = 0x40000aec
                0x40000af0                        slc_send_to_host_chain = 0x40000af0
                0x40000af4                        slc_set_host_io_max_window = 0x40000af4
                0x40000af8                        slc_to_host_chain_recycle = 0x40000af8
                0x40000758                        PROVIDE (esp_rom_crc32_le = crc32_le)
                [!provide]                        PROVIDE (esp_rom_crc16_le = crc16_le)
                [!provide]                        PROVIDE (esp_rom_crc8_le = crc8_le)
                [!provide]                        PROVIDE (esp_rom_crc32_be = crc32_be)
                [!provide]                        PROVIDE (esp_rom_crc16_be = crc16_be)
                [!provide]                        PROVIDE (esp_rom_crc8_be = crc8_be)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_select_gpio = gpio_pad_select_gpio)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_pullup_only = gpio_pad_pullup)
                0x40000704                        PROVIDE (esp_rom_gpio_pad_set_drv = gpio_pad_set_drv)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_unhold = gpio_pad_unhold)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_in_signal = gpio_matrix_in)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_out_signal = gpio_matrix_out)
                [!provide]                        PROVIDE (esp_rom_efuse_mac_address_crc8 = esp_crc8)
                [!provide]                        PROVIDE (esp_rom_efuse_is_secure_boot_enabled = ets_efuse_secure_boot_enabled)
                [!provide]                        PROVIDE (esp_rom_uart_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_uart_tx_one_char = uart_tx_one_char2)
                [!provide]                        PROVIDE (esp_rom_uart_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_uart_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_uart_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_uart_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_uart_putc = ets_write_char_uart)
                0x40000074                        PROVIDE (esp_rom_output_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_output_tx_one_char = uart_tx_one_char)
                0x40000078                        PROVIDE (esp_rom_output_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_output_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_output_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_output_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_output_putc = ets_write_char_uart)
                0x4000074c                        PROVIDE (esp_rom_md5_init = MD5Init)
                0x40000750                        PROVIDE (esp_rom_md5_update = MD5Update)
                0x40000754                        PROVIDE (esp_rom_md5_final = MD5Final)
                0x40000090                        PROVIDE (esp_rom_software_reset_system = software_reset)
                [!provide]                        PROVIDE (esp_rom_software_reset_cpu = software_reset_cpu)
                0x40000028                        PROVIDE (esp_rom_printf = ets_printf)
                0x40000034                        PROVIDE (esp_rom_install_uart_printf = ets_install_uart_printf)
                0x40000040                        PROVIDE (esp_rom_delay_us = ets_delay_us)
                0x40000018                        PROVIDE (esp_rom_get_reset_reason = rtc_get_reset_reason)
                [!provide]                        PROVIDE (esp_rom_route_intr_matrix = intr_matrix_set)
                0x40000044                        PROVIDE (esp_rom_get_cpu_ticks_per_us = ets_get_cpu_frequency)
                0x40000048                        PROVIDE (esp_rom_set_cpu_ticks_per_us = ets_update_cpu_frequency)
                [!provide]                        PROVIDE (esp_rom_spiflash_attach = spi_flash_attach)
                [!provide]                        PROVIDE (esp_rom_spiflash_clear_bp = esp_rom_spiflash_unlock)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_enable = SPI_write_enable)
                [!provide]                        PROVIDE (esp_rom_spiflash_erase_area = SPIEraseArea)
                0x400001d0                        PROVIDE (esp_rom_spiflash_fix_dummylen = spi_dummy_len_fix)
                [!provide]                        PROVIDE (esp_rom_spiflash_set_drvs = SetSpiDrvs)
                [!provide]                        PROVIDE (esp_rom_spiflash_select_padsfunc = SelectSpiFunction)
                [!provide]                        PROVIDE (esp_rom_spiflash_common_cmd = SPI_Common_Command)
                0x400009f4                        __adddf3 = 0x400009f4
                0x400009f8                        __addsf3 = 0x400009f8
                0x400009fc                        __eqdf2 = 0x400009fc
                0x40000a00                        __eqsf2 = 0x40000a00
                0x40000a04                        __extendsfdf2 = 0x40000a04
                0x40000a08                        __fixdfdi = 0x40000a08
                0x40000a0c                        __fixdfsi = 0x40000a0c
                0x40000a10                        __fixsfdi = 0x40000a10
                0x40000a14                        __fixsfsi = 0x40000a14
                0x40000a18                        __fixunsdfsi = 0x40000a18
                0x40000a1c                        __fixunssfdi = 0x40000a1c
                0x40000a20                        __fixunssfsi = 0x40000a20
                0x40000a24                        __floatdidf = 0x40000a24
                0x40000a28                        __floatdisf = 0x40000a28
                0x40000a2c                        __floatsidf = 0x40000a2c
                0x40000a30                        __floatsisf = 0x40000a30
                0x40000a34                        __floatundidf = 0x40000a34
                0x40000a38                        __floatundisf = 0x40000a38
                0x40000a3c                        __floatunsidf = 0x40000a3c
                0x40000a40                        __floatunsisf = 0x40000a40
                0x40000a44                        __gedf2 = 0x40000a44
                0x40000a48                        __gesf2 = 0x40000a48
                0x40000a4c                        __gtdf2 = 0x40000a4c
                0x40000a50                        __gtsf2 = 0x40000a50
                0x40000a54                        __ledf2 = 0x40000a54
                0x40000a58                        __lesf2 = 0x40000a58
                0x40000a5c                        __ltdf2 = 0x40000a5c
                0x40000a60                        __ltsf2 = 0x40000a60
                0x40000a64                        __muldf3 = 0x40000a64
                0x40000a68                        __mulsf3 = 0x40000a68
                0x40000a6c                        __nedf2 = 0x40000a6c
                0x40000a70                        __nesf2 = 0x40000a70
                0x40000a74                        __subdf3 = 0x40000a74
                0x40000a78                        __subsf3 = 0x40000a78
                0x40000a7c                        __truncdfsf2 = 0x40000a7c
                0x40000884                        __absvdi2 = 0x40000884
                0x40000888                        __absvsi2 = 0x40000888
                0x40000894                        __addvdi3 = 0x40000894
                0x40000898                        __addvsi3 = 0x40000898
                0x4000089c                        __ashldi3 = 0x4000089c
                0x400008a0                        __ashrdi3 = 0x400008a0
                0x400008a4                        __bswapdi2 = 0x400008a4
                0x400008a8                        __bswapsi2 = 0x400008a8
                0x400008ac                        __clear_cache = 0x400008ac
                0x400008b0                        __clrsbdi2 = 0x400008b0
                0x400008b4                        __clrsbsi2 = 0x400008b4
                0x400008b8                        __clzdi2 = 0x400008b8
                0x400008bc                        __clzsi2 = 0x400008bc
                0x400008c0                        __cmpdi2 = 0x400008c0
                0x400008c4                        __ctzdi2 = 0x400008c4
                0x400008c8                        __ctzsi2 = 0x400008c8
                0x400008cc                        __divdc3 = 0x400008cc
                0x400008d0                        __divdf3 = 0x400008d0
                0x400008d4                        __divdi3 = 0x400008d4
                0x400008d8                        __divsc3 = 0x400008d8
                0x400008dc                        __divsf3 = 0x400008dc
                0x400008e0                        __divsi3 = 0x400008e0
                0x400008f0                        __ffsdi2 = 0x400008f0
                0x400008f4                        __ffssi2 = 0x400008f4
                0x40000934                        __gcc_bcmp = 0x40000934
                0x40000950                        __lshrdi3 = 0x40000950
                0x4000095c                        __moddi3 = 0x4000095c
                0x40000960                        __modsi3 = 0x40000960
                0x40000964                        __muldc3 = 0x40000964
                0x4000096c                        __muldi3 = 0x4000096c
                0x40000970                        __mulsc3 = 0x40000970
                0x40000978                        __mulsi3 = 0x40000978
                0x4000097c                        __mulvdi3 = 0x4000097c
                0x40000980                        __mulvsi3 = 0x40000980
                0x40000988                        __negdf2 = 0x40000988
                0x4000098c                        __negdi2 = 0x4000098c
                0x40000990                        __negsf2 = 0x40000990
                0x40000994                        __negvdi2 = 0x40000994
                0x40000998                        __negvsi2 = 0x40000998
                0x400009a0                        __paritysi2 = 0x400009a0
                0x400009a4                        __popcountdi2 = 0x400009a4
                0x400009a8                        __popcountsi2 = 0x400009a8
                0x400009ac                        __powidf2 = 0x400009ac
                0x400009b0                        __powisf2 = 0x400009b0
                0x400009bc                        __subvdi3 = 0x400009bc
                0x400009c0                        __subvsi3 = 0x400009c0
                0x400009c8                        __ucmpdi2 = 0x400009c8
                0x400009cc                        __udivdi3 = 0x400009cc
                0x400009d0                        __udivmoddi4 = 0x400009d0
                0x400009d4                        __udivsi3 = 0x400009d4
                0x400009d8                        __udiv_w_sdiv = 0x400009d8
                0x400009dc                        __umoddi3 = 0x400009dc
                0x400009e0                        __umodsi3 = 0x400009e0
                0x400009e4                        __unorddf2 = 0x400009e4
                0x400009e8                        __unordsf2 = 0x400009e8
                0x400009ec                        __extenddftf2 = 0x400009ec
                0x400009f0                        __trunctfdf2 = 0x400009f0
                0x4000039c                        wdt_hal_config_stage = 0x4000039c
                0x400003a0                        wdt_hal_write_protect_disable = 0x400003a0
                0x400003a4                        wdt_hal_write_protect_enable = 0x400003a4
                0x400003a8                        wdt_hal_enable = 0x400003a8
                0x400003ac                        wdt_hal_disable = 0x400003ac
                0x400003b0                        wdt_hal_handle_intr = 0x400003b0
                0x400003b4                        wdt_hal_feed = 0x400003b4
                0x400003b8                        wdt_hal_set_flashboot_en = 0x400003b8
                0x400003bc                        wdt_hal_is_enabled = 0x400003bc
                0x400003c8                        systimer_hal_set_tick_rate_ops = 0x400003c8
                0x400003cc                        systimer_hal_get_counter_value = 0x400003cc
                0x400003d0                        systimer_hal_get_time = 0x400003d0
                0x400003d4                        systimer_hal_set_alarm_target = 0x400003d4
                0x400003d8                        systimer_hal_set_alarm_period = 0x400003d8
                0x400003dc                        systimer_hal_get_alarm_value = 0x400003dc
                0x400003e0                        systimer_hal_enable_alarm_int = 0x400003e0
                0x400003e4                        systimer_hal_on_apb_freq_update = 0x400003e4
                0x400003e8                        systimer_hal_counter_value_advance = 0x400003e8
                0x400003ec                        systimer_hal_enable_counter = 0x400003ec
                0x400003f0                        systimer_hal_select_alarm_mode = 0x400003f0
                0x400003f4                        systimer_hal_connect_alarm_counter = 0x400003f4
                0x400003f8                        systimer_hal_counter_can_stall_by_cpu = 0x400003f8
                0x40000010                        _rom_chip_id = 0x40000010
                0x40000014                        _rom_eco_version = 0x40000014
                0x400004a4                        esp_rom_newlib_init_common_mutexes = 0x400004a4
                0x400004a8                        memset = 0x400004a8
                0x400004c8                        strlen = 0x400004c8
                0x400004cc                        strstr = 0x400004cc
                0x400004d0                        bzero = 0x400004d0
                0x400004d8                        sbrk = 0x400004d8
                0x400004dc                        isalnum = 0x400004dc
                0x400004e0                        isalpha = 0x400004e0
                0x400004e4                        isascii = 0x400004e4
                0x400004e8                        isblank = 0x400004e8
                0x400004ec                        iscntrl = 0x400004ec
                0x400004f0                        isdigit = 0x400004f0
                0x400004f4                        islower = 0x400004f4
                0x400004f8                        isgraph = 0x400004f8
                0x400004fc                        isprint = 0x400004fc
                0x40000500                        ispunct = 0x40000500
                0x40000504                        isspace = 0x40000504
                0x40000508                        isupper = 0x40000508
                0x4000050c                        toupper = 0x4000050c
                0x40000510                        tolower = 0x40000510
                0x40000514                        toascii = 0x40000514
                0x40000518                        memccpy = 0x40000518
                0x4000051c                        memchr = 0x4000051c
                0x40000520                        memrchr = 0x40000520
                0x40000524                        strcasecmp = 0x40000524
                0x40000528                        strcasestr = 0x40000528
                0x4000052c                        strcat = 0x4000052c
                0x40000534                        strchr = 0x40000534
                0x40000538                        strcspn = 0x40000538
                0x4000053c                        strcoll = 0x4000053c
                0x40000540                        strlcat = 0x40000540
                0x40000544                        strlcpy = 0x40000544
                0x40000548                        strlwr = 0x40000548
                0x4000054c                        strncasecmp = 0x4000054c
                0x40000550                        strncat = 0x40000550
                0x40000558                        strnlen = 0x40000558
                0x4000055c                        strrchr = 0x4000055c
                0x40000560                        strsep = 0x40000560
                0x40000564                        strspn = 0x40000564
                0x40000568                        strtok_r = 0x40000568
                0x4000056c                        strupr = 0x4000056c
                0x40000570                        longjmp = 0x40000570
                0x40000574                        setjmp = 0x40000574
                0x40000578                        abs = 0x40000578
                0x4000057c                        div = 0x4000057c
                0x40000580                        labs = 0x40000580
                0x40000584                        ldiv = 0x40000584
                0x40000588                        qsort = 0x40000588
                0x40000598                        utoa = 0x40000598
                0x4000059c                        itoa = 0x4000059c
                0x4087ffd4                        syscall_table_ptr = 0x4087ffd4
                0x4087ffd0                        _global_impure_ptr = 0x4087ffd0
                0x400004ac                        memcpy = 0x400004ac
                0x400004b0                        memmove = 0x400004b0
                0x400004b4                        memcmp = 0x400004b4
                0x400004b8                        strcpy = 0x400004b8
                0x400004bc                        strncpy = 0x400004bc
                0x400004c0                        strcmp = 0x400004c0
                0x400004c4                        strncmp = 0x400004c4
                0x400004d4                        _isatty_r = 0x400004d4
                0x40000530                        strdup = 0x40000530
                0x40000554                        strndup = 0x40000554
                0x4000058c                        rand_r = 0x4000058c
                0x40000590                        rand = 0x40000590
                0x40000594                        srand = 0x40000594
                0x400005a0                        atoi = 0x400005a0
                0x400005a4                        atol = 0x400005a4
                0x400005a8                        strtol = 0x400005a8
                0x400005ac                        strtoul = 0x400005ac
                0x400005b0                        fflush = 0x400005b0
                0x400005b4                        _fflush_r = 0x400005b4
                0x400005b8                        _fwalk = 0x400005b8
                0x400005bc                        _fwalk_reent = 0x400005bc
                0x400005c0                        __smakebuf_r = 0x400005c0
                0x400005c4                        __swhatbuf_r = 0x400005c4
                0x400005c8                        __swbuf_r = 0x400005c8
                0x400005cc                        __swbuf = 0x400005cc
                0x400005d0                        __swsetup_r = 0x400005d0
                [!provide]                        PROVIDE (esprv_int_set_priority = esprv_intc_int_set_priority)
                [!provide]                        PROVIDE (esprv_int_set_threshold = esprv_intc_int_set_threshold)
                [!provide]                        PROVIDE (esprv_int_enable = esprv_intc_int_enable)
                [!provide]                        PROVIDE (esprv_int_disable = esprv_intc_int_disable)
                [!provide]                        PROVIDE (esprv_int_set_type = esprv_intc_int_set_type)
                0x60000000                        PROVIDE (UART0 = 0x60000000)
                [!provide]                        PROVIDE (UART1 = 0x60001000)
                0x60002000                        PROVIDE (SPIMEM0 = 0x60002000)
                0x60003000                        PROVIDE (SPIMEM1 = 0x60003000)
                [!provide]                        PROVIDE (I2C0 = 0x60004000)
                [!provide]                        PROVIDE (UHCI0 = 0x60005000)
                [!provide]                        PROVIDE (RMT = 0x60006000)
                [!provide]                        PROVIDE (RMTMEM = 0x60006400)
                [!provide]                        PROVIDE (LEDC = 0x60007000)
                0x60008000                        PROVIDE (TIMERG0 = 0x60008000)
                0x60009000                        PROVIDE (TIMERG1 = 0x60009000)
                [!provide]                        PROVIDE (SYSTIMER = 0x6000a000)
                [!provide]                        PROVIDE (TWAI0 = 0x6000b000)
                [!provide]                        PROVIDE (I2S0 = 0x6000c000)
                [!provide]                        PROVIDE (TWAI1 = 0x6000d000)
                0x6000e000                        PROVIDE (APB_SARADC = 0x6000e000)
                [!provide]                        PROVIDE (USB_SERIAL_JTAG = 0x6000f000)
                [!provide]                        PROVIDE (INTMTX = 0x60010000)
                [!provide]                        PROVIDE (ATOMIC_LOCKER = 0x60011000)
                [!provide]                        PROVIDE (PCNT = 0x60012000)
                [!provide]                        PROVIDE (SOC_ETM = 0x60013000)
                [!provide]                        PROVIDE (MCPWM0 = 0x60014000)
                [!provide]                        PROVIDE (PARL_IO = 0x60015000)
                [!provide]                        PROVIDE (HINF = 0x60016000)
                [!provide]                        PROVIDE (SLC = 0x60017000)
                [!provide]                        PROVIDE (HOST = 0x60018000)
                [!provide]                        PROVIDE (PVT_MONITOR = 0x60019000)
                [!provide]                        PROVIDE (GDMA = 0x60080000)
                [!provide]                        PROVIDE (GPSPI2 = 0x60081000)
                [!provide]                        PROVIDE (AES = 0x60088000)
                [!provide]                        PROVIDE (SHA = 0x60089000)
                [!provide]                        PROVIDE (RSA = 0x6008a000)
                [!provide]                        PROVIDE (ECC = 0x6008b000)
                [!provide]                        PROVIDE (DS = 0x6008c000)
                [!provide]                        PROVIDE (HMAC = 0x6008d000)
                [!provide]                        PROVIDE (IO_MUX = 0x60090000)
                [!provide]                        PROVIDE (GPIO = 0x60091000)
                [!provide]                        PROVIDE (GPIO_EXT = 0x60091f00)
                [!provide]                        PROVIDE (SDM = 0x60091f00)
                [!provide]                        PROVIDE (GLITCH_FILTER = 0x60091f30)
                [!provide]                        PROVIDE (GPIO_ETM = 0x60091f60)
                [!provide]                        PROVIDE (MEM_MONITOR = 0x60092000)
                [!provide]                        PROVIDE (PAU = 0x60093000)
                [!provide]                        PROVIDE (HP_SYSTEM = 0x60095000)
                0x60096000                        PROVIDE (PCR = 0x60096000)
                [!provide]                        PROVIDE (TEE = 0x60098000)
                [!provide]                        PROVIDE (HP_APM = 0x60099000)
                [!provide]                        PROVIDE (IEEE802154 = 0x600a3000)
                0x600a9800                        PROVIDE (MODEM_SYSCON = 0x600a9800)
                0x600af000                        PROVIDE (MODEM_LPCON = 0x600af000)
                0x600b0000                        PROVIDE (PMU = 0x600b0000)
                0x600b0400                        PROVIDE (LP_CLKRST = 0x600b0400)
                0x600b0800                        PROVIDE (EFUSE = 0x600b0800)
                0x600b0c00                        PROVIDE (LP_TIMER = 0x600b0c00)
                [!provide]                        PROVIDE (LP_AON = 0x600b1000)
                0x600b1400                        PROVIDE (LP_UART = 0x600b1400)
                [!provide]                        PROVIDE (LP_I2C = 0x600b1800)
                0x600b1c00                        PROVIDE (LP_WDT = 0x600b1c00)
                [!provide]                        PROVIDE (LP_IO = 0x600b2000)
                [!provide]                        PROVIDE (LP_I2C_ANA_MST = 0x600b2400)
                [!provide]                        PROVIDE (LPPERI = 0x600b2800)
                0x600b2c00                        PROVIDE (LP_ANA_PERI = 0x600b2c00)
                [!provide]                        PROVIDE (LP_APM = 0x600b3800)
                [!provide]                        PROVIDE (OTP_DEBUG = 0x600b3c00)
                0x4087c610                        bootloader_usable_dram_end = 0x4087c610
                0x00002000                        bootloader_stack_overhead = 0x2000
                0x00005000                        bootloader_dram_seg_len = 0x5000
                0x00007000                        bootloader_iram_loader_seg_len = 0x7000
                0x00002d00                        bootloader_iram_seg_len = 0x2d00
                0x4087a610                        bootloader_dram_seg_end = (bootloader_usable_dram_end - bootloader_stack_overhead)
                0x40875610                        bootloader_dram_seg_start = (bootloader_dram_seg_end - bootloader_dram_seg_len)
                0x4086e610                        bootloader_iram_loader_seg_start = (bootloader_dram_seg_start - bootloader_iram_loader_seg_len)
                0x4086b910                        bootloader_iram_seg_start = (bootloader_iram_loader_seg_start - bootloader_iram_seg_len)
                0x00000001                        ASSERT ((bootloader_iram_loader_seg_start == 0x4086e610), bootloader_iram_loader_seg_start inconsistent with SRAM_DRAM_END)

.iram_loader.text
                0x4086e610     0x31c8
                0x4086e610                        . = ALIGN (0x10)
                0x4086e610                        _loader_text_start = ABSOLUTE (.)
 *(.stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 *(.iram1 .iram1.*)
 .iram1.0       0x4086e610        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
                0x4086e610                esp_flash_encryption_enabled
 .iram1.5       0x4086e614       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .iram1.1       0x4086e664      0x208 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086e664                bootloader_flash_execute_command_common
 .iram1.2       0x4086e86c        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086e86c                bootloader_execute_flash_command
 .iram1.0       0x4086e87a      0x15a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086e87a                bootloader_flash_unlock_default
                0x4086e87a                bootloader_flash_unlock
 .iram1.3       0x4086e9d4       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086e9d4                bootloader_flash_read_sfdp
 .iram1.4       0x4086ea14       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086ea14                bootloader_read_flash_id
 .iram1.6       0x4086ea48       0xbe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086ea48                bootloader_flash_xmc_startup
 .iram1.0       0x4086eb06       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                0x4086eb06                bootloader_flash_cs_timing_config
 .iram1.2       0x4086eb26       0x56 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                0x4086eb26                bootloader_configure_spi_pins
 .iram1.0       0x4086eb7c       0xec .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .iram1.3       0x4086ec68       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
                0x4086ec68                esp_rom_regi2c_write
                0x4086ec68                regi2c_write_impl
 .iram1.4       0x4086ecb8       0xdc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
                0x4086ecb8                regi2c_write_mask_impl
                0x4086ecb8                esp_rom_regi2c_write_mask
 .iram1.0       0x4086ed94       0x1e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                0x4086ed94                efuse_hal_chip_revision
 .iram1.1       0x4086edb2       0x1e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                0x4086edb2                efuse_hal_blk_version
 .iram1.2       0x4086edd0        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                0x4086edd0                efuse_hal_get_disable_wafer_version_major
 .iram1.4       0x4086edde       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                0x4086edde                efuse_hal_get_disable_blk_version_major
 .iram1.5       0x4086edee       0x1e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                0x4086edee                efuse_hal_flash_encryption_enabled
 .iram1.0       0x4086ee0c       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                0x4086ee0c                efuse_hal_get_major_chip_version
 .iram1.1       0x4086ee1c       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                0x4086ee1c                efuse_hal_get_minor_chip_version
 .iram1.2       0x4086ee2c       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
                0x4086ee2c                lp_timer_hal_get_cycle_count
 *liblog.a:(.literal .text .literal.* .text.*)
 .text.esp_log_early_timestamp
                0x4086ee54       0x26 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
                0x4086ee54                esp_log_early_timestamp
                0x4086ee54                esp_log_timestamp
 *libgcc.a:(.literal .text .literal.* .text.*)
 *libclang_rt.builtins.a:(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_clock_loader.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_common_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_common_check_chip_revision_validity
                0x4086ee7a       0xc2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086ee7a                bootloader_common_check_chip_revision_validity
 .text.bootloader_common_ota_select_crc
                0x4086ef3c        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086ef3c                bootloader_common_ota_select_crc
 .text.bootloader_common_ota_select_invalid
                0x4086ef4a       0x16 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086ef4a                bootloader_common_ota_select_invalid
 .text.bootloader_common_ota_select_valid
                0x4086ef60       0x2a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086ef60                bootloader_common_ota_select_valid
 .text.bootloader_common_check_efuse_blk_validity
                0x4086ef8a       0xb4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086ef8a                bootloader_common_check_efuse_blk_validity
 .text.bootloader_common_check_chip_validity
                0x4086f03e       0x64 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086f03e                bootloader_common_check_chip_validity
 .text.bootloader_common_select_otadata
                0x4086f0a2       0x3e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086f0a2                bootloader_common_select_otadata
 .text.bootloader_common_get_active_otadata
                0x4086f0e0       0x2e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                0x4086f0e0                bootloader_common_get_active_otadata
 *libbootloader_support.a:bootloader_flash.*(.literal .text .literal.* .text.*)
 .text.spi_to_esp_err
                0x4086f10e       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .text.bootloader_mmap_get_free_pages
                0x4086f132        0x6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f132                bootloader_mmap_get_free_pages
 .text.bootloader_mmap
                0x4086f138       0xc8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f138                bootloader_mmap
 .text.bootloader_munmap
                0x4086f200       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f200                bootloader_munmap
 .text.bootloader_flash_read
                0x4086f228      0x144 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f228                bootloader_flash_read
 .text.bootloader_flash_erase_sector
                0x4086f36c       0x12 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f36c                bootloader_flash_erase_sector
 .text.bootloader_flash_write
                0x4086f37e       0xd0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f37e                bootloader_flash_write
 .text.bootloader_enable_wp
                0x4086f44e        0xc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f44e                bootloader_enable_wp
 .text.bootloader_flash_get_spi_mode
                0x4086f45a       0x3e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                0x4086f45a                bootloader_flash_get_spi_mode
 *libbootloader_support.a:bootloader_random.*(.literal .text .literal.* .text.*)
 .text.bootloader_fill_random
                0x4086f498       0x80 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
                0x4086f498                bootloader_fill_random
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_disable .text.bootloader_random_disable)
 .text.bootloader_random_disable
                0x4086f518      0x10c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
                0x4086f518                bootloader_random_disable
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_enable .text.bootloader_random_enable)
 .text.bootloader_random_enable
                0x4086f624      0x1cc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
                0x4086f624                bootloader_random_enable
 *libbootloader_support.a:bootloader_efuse.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_utility.*(.literal .text .literal.* .text.*)
 .text.log_invalid_app_partition
                0x4086f7f0       0x84 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.index_to_partition
                0x4086f874       0x46 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.try_load_partition
                0x4086f8ba       0x44 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.set_actual_ota_seq
                0x4086f8fe       0xaa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.load_image
                0x4086f9a8      0x1d2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .text.bootloader_common_read_otadata
                0x4086fb7a       0xa2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                0x4086fb7a                bootloader_common_read_otadata
 .text.bootloader_utility_load_partition_table
                0x4086fc1c      0x27e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                0x4086fc1c                bootloader_utility_load_partition_table
 .text.bootloader_utility_get_selected_boot_partition
                0x4086fe9a      0x112 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                0x4086fe9a                bootloader_utility_get_selected_boot_partition
 .text.bootloader_reset
                0x4086ffac       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                0x4086ffac                bootloader_reset
 .text.bootloader_utility_load_boot_image
                0x4086ffc8      0x11c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                0x4086ffc8                bootloader_utility_load_boot_image
 .text.bootloader_debug_buffer
                0x408700e4        0x2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                0x408700e4                bootloader_debug_buffer
 *libbootloader_support.a:bootloader_utility_tee.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_sha.*(.literal .text .literal.* .text.*)
 .text.bootloader_sha256_start
                0x408700e6       0x2c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                0x408700e6                bootloader_sha256_start
 .text.bootloader_sha256_data
                0x40870112       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                0x40870112                bootloader_sha256_data
 .text.bootloader_sha256_finish
                0x40870146       0x46 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                0x40870146                bootloader_sha256_finish
 *libbootloader_support.a:bootloader_console_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_console_deinit
                0x4087018c        0xa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
                0x4087018c                bootloader_console_deinit
 *libbootloader_support.a:bootloader_panic.*(.literal .text .literal.* .text.*)
 .text.__assert_func
                0x40870196       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
                0x40870196                __assert_func
 .text.unlikely.abort
                0x408701b6       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
                0x408701b6                abort
 *libbootloader_support.a:bootloader_soc.*(.literal .text .literal.* .text.*)
 .text.bootloader_ana_super_wdt_reset_config
                0x408701d8       0x32 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
                0x408701d8                bootloader_ana_super_wdt_reset_config
 .text.bootloader_ana_clock_glitch_reset_config
                0x4087020a        0x2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
                0x4087020a                bootloader_ana_clock_glitch_reset_config
 *libbootloader_support.a:esp_image_format.*(.literal .text .literal.* .text.*)
 .text.should_load
                0x4087020c       0x4e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.bootloader_util_regions_overlap
                0x4087025a       0x4c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.process_segments
                0x408702a6      0x50c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.image_load
                0x408707b2      0x406 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .text.bootloader_load_image
                0x40870bb8        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                0x40870bb8                bootloader_load_image
 *libbootloader_support.a:flash_encrypt.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_encryption_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_partitions.*(.literal .text .literal.* .text.*)
 .text.esp_partition_table_verify
                0x40870bc0      0x18a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
                0x40870bc0                esp_partition_table_verify
 *libbootloader_support.a:secure_boot.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_signatures_bootloader.*(.literal .text .literal.* .text.*)
 *libmicro-ecc.a:*.*(.literal .text .literal.* .text.*)
 *libspi_flash.a:*.*(.literal .text .literal.* .text.*)
 *libhal.a:wdt_hal_iram.*(.literal .text .literal.* .text.*)
 *libhal.a:mmu_hal.*(.literal .text .literal.* .text.*)
 .text.mmu_ll_check_valid_paddr_region.isra.0
                0x40870d4a       0x86 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .text.mmu_hal_unmap_all
                0x40870dd0       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                0x40870dd0                mmu_hal_unmap_all
 .text.mmu_hal_init
                0x40870df4       0x24 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                0x40870df4                mmu_hal_init
 .text.mmu_hal_pages_to_bytes
                0x40870e18       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                0x40870e18                mmu_hal_pages_to_bytes
 .text.mmu_hal_check_valid_ext_vaddr_region
                0x40870e48       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                0x40870e48                mmu_hal_check_valid_ext_vaddr_region
 .text.mmu_hal_map_region
                0x40870e68      0x10a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                0x40870e68                mmu_hal_map_region
 *libhal.a:cache_hal.*(.literal .text .literal.* .text.*)
 .text.s_cache_hal_init_ctx
                0x40870f72       0x32 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                0x40870f72                s_cache_hal_init_ctx
 .text.cache_hal_init
                0x40870fa4       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                0x40870fa4                cache_hal_init
 .text.s_update_cache_state
                0x40870fec       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                0x40870fec                s_update_cache_state
 .text.cache_hal_disable
                0x40871020       0x2a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                0x40871020                cache_hal_disable
 .text.cache_hal_enable
                0x4087104a       0x2e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                0x4087104a                cache_hal_enable
 *libhal.a:efuse_hal.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:rtc_clk.*(.literal .text .literal.* .text.*)
 .text.rtc_clk_bbpll_disable
                0x40871078       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_freq_to_8m
                0x4087109a       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_freq_to_xtal
                0x408710e2       0x5a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_cpu_freq_to_pll_mhz
                0x4087113c       0x84 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_32k_enable.part.0
                0x408711c0       0x54 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .text.rtc_clk_32k_enable
                0x40871214       0x1a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x40871214                rtc_clk_32k_enable
 .text.rtc_clk_32k_enable_external
                0x4087122e       0x1a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x4087122e                rtc_clk_32k_enable_external
 .text.rtc_clk_rc32k_enable
                0x40871248       0x2c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x40871248                rtc_clk_rc32k_enable
 .text.rtc_clk_8m_enable
                0x40871274       0x2c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x40871274                rtc_clk_8m_enable
 .text.rtc_clk_slow_src_set
                0x408712a0       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x408712a0                rtc_clk_slow_src_set
 .text.rtc_clk_slow_src_get
                0x408712f0       0x1e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x408712f0                rtc_clk_slow_src_get
 .text.rtc_clk_slow_freq_get_hz
                0x4087130e       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x4087130e                rtc_clk_slow_freq_get_hz
 .text.rtc_clk_fast_src_set
                0x4087132e       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x4087132e                rtc_clk_fast_src_set
 .text.rtc_clk_fast_src_get
                0x4087135e        0xe .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x4087135e                rtc_clk_fast_src_get
 .text.rtc_clk_set_cpu_switch_to_pll
                0x4087136c        0x2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x4087136c                rtc_clk_set_cpu_switch_to_pll
 .text.rtc_clk_xtal_freq_get
                0x4087136e       0x70 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x4087136e                rtc_clk_xtal_freq_get
                0x4087136e                rtc_get_xtal
 .text.rtc_clk_cpu_freq_mhz_to_config
                0x408713de       0x6a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x408713de                rtc_clk_cpu_freq_mhz_to_config
 .text.rtc_clk_cpu_freq_set_config
                0x40871448      0x17e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x40871448                rtc_clk_cpu_freq_set_config
 .text.rtc_clk_cpu_freq_get_config
                0x408715c6       0xf0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x408715c6                rtc_clk_cpu_freq_get_config
 .text.rtc_clk_xtal_freq_update
                0x408716b6       0x26 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x408716b6                rtc_clk_xtal_freq_update
 .text.rtc_clk_apb_freq_get
                0x408716dc       0xf0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                0x408716dc                rtc_clk_apb_freq_get
 *libesp_hw_support.a:rtc_time.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:regi2c_ctrl.*(.literal .text .literal.* .text.*)
 *libefuse.a:*.*(.literal .text .literal.* .text.*)
 *libriscv.a:rv_utils.*(.literal .text .literal.* .text.*)
 .text.rv_utils_dbgr_is_attached
                0x408717cc        0xc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
                0x408717cc                rv_utils_dbgr_is_attached
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x408717d8                        _loader_text_end = ABSOLUTE (.)

.iram.text      0x4086b910        0x0
                0x4086b910                        . = ALIGN (0x10)
 *(.entry.text)
 *(.init.literal)
 *(.init)

.dram0.bss      0x40875610      0x118
                0x40875610                        . = ALIGN (0x8)
                0x40875610                        _dram_start = ABSOLUTE (.)
                0x40875610                        _bss_start = ABSOLUTE (.)
 *(.dynsbss)
 *(.sbss)
 *(.sbss.*)
 .sbss.ota_has_initial_contents
                0x40875610        0x1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 *fill*         0x40875611        0x3 
 .sbss.s_bootloader_partition_offset
                0x40875614        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .sbss.ram_obfs_value
                0x40875618        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .sbss.mapped   0x40875620        0x1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 *fill*         0x40875621        0x3 
 .sbss.s_bbpll_digi_consumers_ref_count
                0x40875624        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .sbss.s_cur_pll_freq
                0x40875628        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .sbss.ctx      0x4087562c        0x8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
 *(.gnu.linkonce.sb.*)
 *(.scommon)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(.dynbss)
 *(.bss)
 *(.bss.*)
 .bss.ctx       0x40875634       0xd8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .bss.bootloader_image_hdr
                0x4087570c       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                0x4087570c                bootloader_image_hdr
 *(.gnu.linkonce.b.*)
 *(COMMON)
                0x40875728                        . = ALIGN (0x8)
 *fill*         0x40875724        0x4 
                0x40875728                        _bss_end = ABSOLUTE (.)

.dram0.bootdesc
                0x40875730       0x50
                0x40875730                        _data_start = ABSOLUTE (.)
 *(.data_bootloader_desc .data_bootloader_desc.*)
 .data_bootloader_desc
                0x40875730       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
                0x40875730                esp_bootloader_desc

.dram0.data     0x40875780        0x8
 *(.dram1 .dram1.*)
 .dram1.0       0x40875780        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 *(.data)
 *(.data.*)
 *(.gnu.linkonce.d.*)
 *(.data1)
 *(.sdata)
 *(.sdata.*)
 .sdata.current_read_mapping
                0x40875784        0x4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 *(.gnu.linkonce.s.*)
 *(.gnu.linkonce.s2.*)
 *(.jcr)
                0x40875788                        _data_end = ABSOLUTE (.)

.dram0.rodata   0x40875788     0x16ec
                0x40875788                        _rodata_start = ABSOLUTE (.)
 *(.rodata)
 *(.rodata.*)
 .rodata.call_start_cpu0.str1.4
                0x40875788     0x15ae .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
                                 0x31 (size before relaxing)
 .rodata.log_invalid_app_partition.str1.4
                0x40876d36       0x99 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.try_load_partition.str1.4
                0x40876d36       0x37 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.set_actual_ota_seq.str1.4
                0x40876d36       0x6e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.load_image.str1.4
                0x40876d36       0x93 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.bootloader_common_read_otadata.str1.4
                0x40876d36       0x7e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.bootloader_utility_load_partition_table.str1.4
                0x40876d36      0x1fc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.bootloader_utility_get_selected_boot_partition.str1.4
                0x40876d36       0xec .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.bootloader_utility_load_boot_image.str1.4
                0x40876d36       0xc3 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 *fill*         0x40876d36        0x2 
 .rodata.__func__.0
                0x40876d38       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .rodata.esp_partition_table_verify.str1.4
                0x40876d48      0x131 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .rodata.bootloader_util_regions_overlap.str1.4
                0x40876d48       0x5e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .rodata.process_segments.str1.4
                0x40876d48      0x2f2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .rodata.image_load.str1.4
                0x40876d48      0x192 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .rodata.__func__.0
                0x40876d48       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .rodata.__func__.1
                0x40876d68       0x16 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .rodata.bootloader_sha256_data.str1.4
                0x40876d7e       0x49 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 *fill*         0x40876d7e        0x2 
 .rodata.__func__.0
                0x40876d80       0x19 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 *fill*         0x40876d99        0x3 
 .rodata.__func__.1
                0x40876d9c       0x17 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .rodata.bootloader_ana_super_wdt_reset_config.str1.4
                0x40876db3       0x49 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 *fill*         0x40876db3        0x1 
 .rodata.__func__.0
                0x40876db4       0x26 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .rodata.bootloader_init.str1.4
                0x40876dda       0xf8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 *fill*         0x40876dda        0x2 
 .rodata.__func__.0
                0x40876ddc       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .rodata.__assert_func.str1.4
                0x40876dec       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .rodata.abort.str1.4
                0x40876dec       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .rodata.bootloader_common_check_chip_revision_validity.str1.4
                0x40876dec       0xa3 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .rodata.bootloader_common_check_efuse_blk_validity.str1.4
                0x40876dec       0x98 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .rodata.bootloader_common_check_chip_validity.str1.4
                0x40876dec       0x35 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .rodata.bootloader_fill_random.str1.4
                0x40876dec       0x4c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .rodata.__func__.0
                0x40876dec       0x17 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .rodata.bootloader_mmap.str1.4
                0x40876e03       0x8d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .rodata.bootloader_flash_read.str1.4
                0x40876e03       0xc4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .rodata.str1.4
                0x40876e03       0xc8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .rodata.bootloader_flash_write.str1.4
                0x40876e03       0xcc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 *fill*         0x40876e03        0x1 
 .rodata.__func__.1
                0x40876e04       0x1b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 *fill*         0x40876e1f        0x1 
 .rodata.__func__.0
                0x40876e20       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .rodata.bootloader_init_spi_flash.str1.4
                0x40876e48       0xc9 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .rodata.bootloader_read_bootloader_header.str1.4
                0x40876e48       0x3d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .rodata.bootloader_check_bootloader_validity.str1.4
                0x40876e48       0x4e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .rodata.bootloader_enable_random.str1.4
                0x40876e48       0x32 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .rodata.bootloader_print_banner.str1.4
                0x40876e48       0x4d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .rodata.rtc_clk_init.str1.4
                0x40876e48       0x39 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .rodata.rtc_clk_xtal_freq_get.str1.4
                0x40876e48       0x43 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .rodata.rtc_clk_cpu_freq_get_config.str1.4
                0x40876e48       0x31 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .rodata.rtc_clk_apb_freq_get.str1.4
                0x40876e48       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .rodata.str1.4
                0x40876e48       0x7a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .rodata.__func__.2
                0x40876e48       0x17 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 *fill*         0x40876e5f        0x1 
 .rodata.__func__.0
                0x40876e60       0x14 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 *(.gnu.linkonce.r.*)
 *(.rodata1)
 *(.sdata2 .sdata2.* .srodata .srodata.*)
                0x40876e74                        __XT_EXCEPTION_TABLE_ = ABSOLUTE (.)
 *(.xt_except_table)
 *(.gcc_except_table)
 *(.gnu.linkonce.e.*)
 *(.gnu.version_r)
 *(.eh_frame_hdr)
 *(.eh_frame)
                0x40876e74                        . = ((. + 0x3) & 0xfffffffffffffffc)
                0x40876e74                        __init_array_start = ABSOLUTE (.)
 *crtbegin.*(.ctors)
 *(EXCLUDE_FILE(*crtend.*) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
                0x40876e74                        __init_array_end = ABSOLUTE (.)
 *crtbegin.*(.dtors)
 *(EXCLUDE_FILE(*crtend.*) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
                0x40876e74                        __XT_EXCEPTION_DESCS_ = ABSOLUTE (.)
 *(.xt_except_desc)
 *(.gnu.linkonce.h.*)
                0x40876e74                        __XT_EXCEPTION_DESCS_END__ = ABSOLUTE (.)
 *(.xt_except_desc_end)
 *(.dynamic)
 *(.gnu.version_d)
                0x40876e74                        _rodata_end = ABSOLUTE (.)
                0x40876e74                        _lit4_start = ABSOLUTE (.)
 *(*.lit4)
 *(.lit4.*)
 *(.gnu.linkonce.lit4.*)
                0x40876e74                        _lit4_end = ABSOLUTE (.)
                0x40876e74                        . = ALIGN (0x4)
                0x40876e74                        _dram_end = ABSOLUTE (.)

.iram.text      0x4086b910      0xe74
                0x4086b910                        _stext = .
                0x4086b910                        _text_start = ABSOLUTE (.)
 *(.literal .text .literal.* .text.* .stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 .text.call_start_cpu0
                0x4086b910       0x82 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
                0x4086b910                call_start_cpu0
 .text.bootloader_init
                0x4086b992      0x1c6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
                0x4086b992                bootloader_init
 .text.bootloader_clock_configure
                0x4086bb58      0x148 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
                0x4086bb58                bootloader_clock_configure
 .text.bootloader_init_mem
                0x4086bca0        0x2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
                0x4086bca0                bootloader_init_mem
 .text.bootloader_flash_update_id
                0x4086bca2       0x1c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                0x4086bca2                bootloader_flash_update_id
 .text.bootloader_init_spi_flash
                0x4086bcbe      0x1be .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                0x4086bcbe                bootloader_init_spi_flash
 .text.bootloader_clear_bss_section
                0x4086be7c       0x22 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                0x4086be7c                bootloader_clear_bss_section
 .text.bootloader_read_bootloader_header
                0x4086be9e       0x3e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                0x4086be9e                bootloader_read_bootloader_header
 .text.bootloader_check_bootloader_validity
                0x4086bedc       0x84 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                0x4086bedc                bootloader_check_bootloader_validity
 .text.bootloader_config_wdt
                0x4086bf60       0xaa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                0x4086bf60                bootloader_config_wdt
 .text.bootloader_enable_random
                0x4086c00a       0x2a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                0x4086c00a                bootloader_enable_random
 .text.bootloader_print_banner
                0x4086c034       0x54 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                0x4086c034                bootloader_print_banner
 .text.bootloader_console_init
                0x4086c088       0xea .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
                0x4086c088                bootloader_console_init
 .text.esp_bootloader_get_description
                0x4086c172        0xa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
                0x4086c172                esp_bootloader_get_description
 .text.esp_cpu_configure_region_protection
                0x4086c17c      0x198 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
                0x4086c17c                esp_cpu_configure_region_protection
 .text.rtc_clk_init
                0x4086c314      0x20c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                0x4086c314                rtc_clk_init
 .text.get_act_hp_dbias
                0x4086c520       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
                0x4086c520                get_act_hp_dbias
 .text.get_act_lp_dbias
                0x4086c554       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
                0x4086c554                get_act_lp_dbias
 .text.wdt_hal_init
                0x4086c588      0x1ec .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
                0x4086c588                wdt_hal_init
 *(.iram .iram.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x4086c784                        . = (. + 0x10)
 *fill*         0x4086c774       0x10 
                0x4086c784                        _text_end = ABSOLUTE (.)
                0x4086c784                        _etext = .

.riscv.attributes
                0x00000000       0x60
 *(.riscv.attributes)
 .riscv.attributes
                0x00000000       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .riscv.attributes
                0x0000005c       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .riscv.attributes
                0x000000b8       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .riscv.attributes
                0x00000114       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .riscv.attributes
                0x00000170       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .riscv.attributes
                0x000001cc       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .riscv.attributes
                0x00000228       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .riscv.attributes
                0x00000284       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .riscv.attributes
                0x000002e0       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .riscv.attributes
                0x0000033c       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .riscv.attributes
                0x00000398       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .riscv.attributes
                0x000003f4       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .riscv.attributes
                0x00000450       0x60 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .riscv.attributes
                0x000004b0       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .riscv.attributes
                0x0000050c       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .riscv.attributes
                0x00000568       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .riscv.attributes
                0x000005c4       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .riscv.attributes
                0x00000620       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .riscv.attributes
                0x0000067c       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .riscv.attributes
                0x000006d8       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .riscv.attributes
                0x00000734       0x60 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .riscv.attributes
                0x00000794       0x60 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .riscv.attributes
                0x000007f4       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .riscv.attributes
                0x00000850       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .riscv.attributes
                0x000008ac       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .riscv.attributes
                0x00000908       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .riscv.attributes
                0x00000964       0x60 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .riscv.attributes
                0x000009c4       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .riscv.attributes
                0x00000a20       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .riscv.attributes
                0x00000a7c       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .riscv.attributes
                0x00000ad8       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .riscv.attributes
                0x00000b34       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .riscv.attributes
                0x00000b90       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug
 *(.debug)

.line
 *(.line)

.debug_srcinfo
 *(.debug_srcinfo)

.debug_sfnames
 *(.debug_sfnames)

.debug_aranges  0x00000000      0x908
 *(.debug_aranges)
 .debug_aranges
                0x00000000       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .debug_aranges
                0x00000028       0x90 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_aranges
                0x000000b8       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_aranges
                0x000000d8       0x80 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_aranges
                0x00000158       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_aranges
                0x00000188       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .debug_aranges
                0x000001a8       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_aranges
                0x000001d0       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_aranges
                0x000001f0       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_aranges
                0x00000218       0x58 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_aranges
                0x00000270       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .debug_aranges
                0x00000290       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .debug_aranges
                0x000002b0       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_aranges
                0x000002d0       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_aranges
                0x00000310       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_aranges
                0x00000338       0xb8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_aranges
                0x000003f0       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_aranges
                0x00000438       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_aranges
                0x00000480       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_aranges
                0x000004a0       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .debug_aranges
                0x000004c0       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_aranges
                0x000004e0       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_aranges
                0x00000500      0x130 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_aranges
                0x00000630       0x60 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_aranges
                0x00000690       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_aranges
                0x000006d0       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_aranges
                0x000006f8       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .debug_aranges
                0x00000718       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .debug_aranges
                0x00000738       0x50 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_aranges
                0x00000788       0x58 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_aranges
                0x000007e0       0x38 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_aranges
                0x00000818       0x68 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_aranges
                0x00000880       0x88 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_pubnames
 *(.debug_pubnames)

.debug_info     0x00000000    0x426e0
 *(.debug_info .gnu.linkonce.wi.*)
 .debug_info    0x00000000      0xd59 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .debug_info    0x00000d59     0x21be .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_info    0x00002f17      0x678 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_info    0x0000358f     0x2911 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_info    0x00005ea0      0x44e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_info    0x000062ee       0xc6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .debug_info    0x000063b4      0x15f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_info    0x00006513     0x45c1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_info    0x0000aad4      0x1f6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_info    0x0000acca      0xbce .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_info    0x0000b898      0x36d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .debug_info    0x0000bc05       0xa6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .debug_info    0x0000bcab      0x2c4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_info    0x0000bf6f      0xb0c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_info    0x0000ca7b     0x4abf .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_info    0x0001153a     0x5b01 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_info    0x0001703b      0xcb8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_info    0x00017cf3     0x20bc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_info    0x00019daf     0x4a56 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_info    0x0001e805      0x18e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .debug_info    0x0001e993      0x366 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_info    0x0001ecf9     0x7151 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_info    0x00025e4a     0x52b1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_info    0x0002b0fb     0x499c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_info    0x0002fa97      0xc32 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_info    0x000306c9     0x4ce3 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_info    0x000353ac      0x13e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .debug_info    0x000354ea       0xd1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .debug_info    0x000355bb     0x3869 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_info    0x00038e24     0x3a97 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_info    0x0003c8bb      0x999 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_info    0x0003d254     0x457e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_info    0x000417d2      0xf0e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_abbrev   0x00000000     0x5c6f
 *(.debug_abbrev)
 .debug_abbrev  0x00000000      0x33b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .debug_abbrev  0x0000033b      0x540 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_abbrev  0x0000087b      0x23e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_abbrev  0x00000ab9      0x554 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_abbrev  0x0000100d      0x1ca .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_abbrev  0x000011d7       0x89 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .debug_abbrev  0x00001260       0xdc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_abbrev  0x0000133c      0x421 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_abbrev  0x0000175d      0x11b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_abbrev  0x00001878      0x2b9 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_abbrev  0x00001b31      0x143 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .debug_abbrev  0x00001c74       0x65 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .debug_abbrev  0x00001cd9      0x19b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_abbrev  0x00001e74      0x34c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_abbrev  0x000021c0      0x3c2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_abbrev  0x00002582      0x57c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_abbrev  0x00002afe      0x328 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_abbrev  0x00002e26      0x347 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_abbrev  0x0000316d      0x376 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_abbrev  0x000034e3       0xaa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .debug_abbrev  0x0000358d      0x163 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_abbrev  0x000036f0      0x4f8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_abbrev  0x00003be8      0x630 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_abbrev  0x00004218      0x327 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_abbrev  0x0000453f      0x202 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_abbrev  0x00004741      0x39a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_abbrev  0x00004adb       0xec .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .debug_abbrev  0x00004bc7       0x7b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .debug_abbrev  0x00004c42      0x22d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_abbrev  0x00004e6f      0x37a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_abbrev  0x000051e9      0x2b1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_abbrev  0x0000549a      0x43b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_abbrev  0x000058d5      0x39a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_line     0x00000000    0x17393
 *(.debug_line)
 .debug_line    0x00000000      0x6ef .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .debug_line    0x000006ef     0x2810 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_line    0x00002eff      0x7ed .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_line    0x000036ec     0x28ad .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_line    0x00005f99      0x427 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_line    0x000063c0      0x1e2 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .debug_line    0x000065a2      0x243 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_line    0x000067e5      0xc8d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_line    0x00007472      0x3f6 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_line    0x00007868      0xb65 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_line    0x000083cd      0x5b1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .debug_line    0x0000897e      0x107 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .debug_line    0x00008a85      0x4d4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_line    0x00008f59     0x1072 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_line    0x00009fcb      0xacc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_line    0x0000aa97     0x1f59 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_line    0x0000c9f0      0xc01 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_line    0x0000d5f1      0xcc7 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_line    0x0000e2b8      0x65b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_line    0x0000e913      0x204 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .debug_line    0x0000eb17      0x75f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_line    0x0000f276      0xc8b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_line    0x0000ff01     0x20e5 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_line    0x00011fe6      0x942 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_line    0x00012928      0x863 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_line    0x0001318b      0xd0b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_line    0x00013e96      0x2fa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .debug_line    0x00014190      0x1e9 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .debug_line    0x00014379      0x4d1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_line    0x0001484a      0x719 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_line    0x00014f63      0x462 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_line    0x000153c5     0x13d4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_line    0x00016799      0xbfa .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_frame    0x00000000     0x1820
 *(.debug_frame)
 .debug_frame   0x00000000       0x38 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .debug_frame   0x00000038      0x26c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_frame   0x000002a4       0x58 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_frame   0x000002fc      0x1c8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_frame   0x000004c4       0x68 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_frame   0x0000052c       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .debug_frame   0x0000054c       0x3c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_frame   0x00000588       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_frame   0x000005bc       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_frame   0x000005fc      0x120 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_frame   0x0000071c       0x38 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .debug_frame   0x00000754       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .debug_frame   0x00000774       0x44 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_frame   0x000007b8       0xb4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_frame   0x0000086c       0x5c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_frame   0x000008c8      0x2d4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_frame   0x00000b9c       0xac .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_frame   0x00000c48       0xc4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_frame   0x00000d0c       0x34 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_frame   0x00000d40       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .debug_frame   0x00000d60       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_frame   0x00000d90       0x38 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_frame   0x00000dc8      0x33c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_frame   0x00001104      0x10c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_frame   0x00001210       0xdc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_frame   0x000012ec       0x54 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_frame   0x00001340       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .debug_frame   0x00001370       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .debug_frame   0x00001390       0x90 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_frame   0x00001420       0xb0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_frame   0x000014d0       0x60 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_frame   0x00001530      0x170 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_frame   0x000016a0      0x180 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_str      0x00000000    0x12235
 *(.debug_str)
 .debug_str     0x00000000    0x12235 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
                                0xafc (size before relaxing)
 .debug_str     0x00012235     0x1a9d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_str     0x00012235      0x57f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_str     0x00012235     0x1b7a .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_str     0x00012235      0x428 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_str     0x00012235      0x2a3 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .debug_str     0x00012235      0x2e3 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_str     0x00012235     0x39cd .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_str     0x00012235      0x301 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_str     0x00012235     0x1264 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_str     0x00012235      0x72b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .debug_str     0x00012235      0x292 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .debug_str     0x00012235      0x372 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_str     0x00012235      0xae9 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_str     0x00012235     0x31fc .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_str     0x00012235     0x3e0e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_str     0x00012235     0x156c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_str     0x00012235     0x1df9 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_str     0x00012235     0x2955 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_str     0x00012235      0x30e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .debug_str     0x00012235      0x367 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_str     0x00012235     0x55df .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_str     0x00012235     0x3c6c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_str     0x00012235     0x342c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_str     0x00012235      0x9ea .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_str     0x00012235     0x2bde .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_str     0x00012235      0x2df .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .debug_str     0x00012235      0x294 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .debug_str     0x00012235     0x29a3 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_str     0x00012235     0x2b1e .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_str     0x00012235      0x5ee .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_str     0x00012235     0x2bd1 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_str     0x00012235      0x749 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_loc      0x00000000     0x9f9b
 *(.debug_loc)
 .debug_loc     0x00000000       0xc7 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .debug_loc     0x000000c7     0x1118 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_loc     0x000011df      0x1d9 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_loc     0x000013b8     0x263d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_loc     0x000039f5      0x17d .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_loc     0x00003b72       0x32 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_loc     0x00003ba4       0xd8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_loc     0x00003c7c       0xc8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_loc     0x00003d44      0x49c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_loc     0x000041e0      0x19c .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_loc     0x0000437c      0x1e5 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_loc     0x00004561      0x394 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_loc     0x000048f5     0x15a4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_loc     0x00005e99      0x135 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_loc     0x00005fce      0x141 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_loc     0x0000610f       0x65 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_loc     0x00006174       0x60 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_loc     0x000061d4      0x1b0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_loc     0x00006384      0xd9b .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_loc     0x0000711f      0x268 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_loc     0x00007387      0x721 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_loc     0x00007aa8      0x518 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_loc     0x00007fc0       0x13 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_loc     0x00007fd3      0x182 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_loc     0x00008155      0x175 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_loc     0x000082ca      0xfed .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_loc     0x000092b7      0xce4 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_macinfo
 *(.debug_macinfo)

.debug_pubtypes
 *(.debug_pubtypes)

.debug_ranges   0x00000000     0x2130
 *(.debug_ranges)
 .debug_ranges  0x00000000       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .debug_ranges  0x00000030      0x228 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .debug_ranges  0x00000258       0x70 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .debug_ranges  0x000002c8      0x420 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .debug_ranges  0x000006e8       0x20 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .debug_ranges  0x00000708       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .debug_ranges  0x00000718       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .debug_ranges  0x00000730       0xd0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .debug_ranges  0x00000800       0x18 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .debug_ranges  0x00000818       0x48 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .debug_ranges  0x00000860       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .debug_ranges  0x00000870       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .debug_ranges  0x00000880       0x40 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .debug_ranges  0x000008c0       0x90 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .debug_ranges  0x00000950      0x1f0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .debug_ranges  0x00000b40      0x2a0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .debug_ranges  0x00000de0       0xa8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .debug_ranges  0x00000e88       0x70 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .debug_ranges  0x00000ef8       0x58 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .debug_ranges  0x00000f50       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .debug_ranges  0x00000f60       0x58 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .debug_ranges  0x00000fb8      0x108 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .debug_ranges  0x000010c0      0x628 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .debug_ranges  0x000016e8       0x68 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .debug_ranges  0x00001750       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .debug_ranges  0x00001780      0x280 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .debug_ranges  0x00001a00       0x28 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .debug_ranges  0x00001a28       0x10 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .debug_ranges  0x00001a38       0x90 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_ranges  0x00001ac8       0x98 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .debug_ranges  0x00001b60       0x88 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .debug_ranges  0x00001be8      0x3c8 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .debug_ranges  0x00001fb0      0x180 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.debug_weaknames
 *(.debug_weaknames)

.debug_funcnames
 *(.debug_funcnames)

.debug_typenames
 *(.debug_typenames)

.debug_varnames
 *(.debug_varnames)

.debug_gnu_pubnames
 *(.debug_gnu_pubnames)

.debug_gnu_pubtypes
 *(.debug_gnu_pubtypes)

.debug_types
 *(.debug_types)

.debug_addr
 *(.debug_addr)

.debug_line_str
 *(.debug_line_str)

.debug_loclists
 *(.debug_loclists)

.debug_macro
 *(.debug_macro)

.debug_names
 *(.debug_names)

.debug_rnglists
 *(.debug_rnglists)

.debug_str_offsets
 *(.debug_str_offsets)

.comment        0x00000000       0x2f
 *(.comment)
 .comment       0x00000000       0x2f .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
                                 0x30 (size before relaxing)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .comment       0x0000002f       0x30 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

.note.GNU-stack
                0x00000000        0x0
 *(.note.GNU-stack)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
 .note.GNU-stack
                0x00000000        0x0 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)

/DISCARD/
 *(.rela.*)

.noload         0x00000000        0x0
                0x00000000                        _noload_keep_in_elf_start = ABSOLUTE (.)
 *(.noload_keep_in_elf .noload_keep_in_elf.*)
                0x00000000                        _noload_keep_in_elf_end = ABSOLUTE (.)
OUTPUT(.pio\build\esp32-c6-devkitc-1\bootloader.elf elf32-littleriscv)

Cross Reference Table

Symbol                                            File
APB_SARADC                                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
Cache_Disable_ICache                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
Cache_Enable_ICache                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
Cache_Freeze_ICache_Disable                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
Cache_Freeze_ICache_Enable                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
Cache_Get_ICache_Line_Size                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
Cache_Invalidate_Addr                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
Cache_Resume_ICache                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
Cache_Suspend_ICache                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
EFUSE                                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
ESP_EFUSE_ACTIVE_HP_DBIAS                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ACTIVE_LP_DBIAS                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN0                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN1                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN2                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN3                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH0               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH1               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH2               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH3               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH4               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH5               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH6               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN1                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN2                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN3                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_BLK_VERSION_MAJOR                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_BLK_VERSION_MINOR                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_CRYPT_DPA_ENABLE                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DBIAS_VOL_GAP                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DIS_DIRECT_BOOT                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_DIS_DOWNLOAD_ICACHE                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_DIS_DOWNLOAD_MODE                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
ESP_EFUSE_DIS_FORCE_DOWNLOAD                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DIS_ICACHE                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DIS_PAD_JTAG                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_DIS_TWAI                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DIS_USB_JTAG                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_DIS_USB_SERIAL_JTAG                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DSLP_LP_DBG                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_DSLP_LP_DBIAS                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
ESP_EFUSE_FLASH_CAP                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_FLASH_TEMP                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_FLASH_TPUW                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_FLASH_VENDOR                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_FORCE_SEND_RESUME                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_JTAG_SEL_ENABLE                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_KEY0                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY1                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY2                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY3                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY4                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY5                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY_PURPOSE_0                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY_PURPOSE_1                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY_PURPOSE_2                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY_PURPOSE_3                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY_PURPOSE_4                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_KEY_PURPOSE_5                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_LSLP_HP_DBG                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_LSLP_HP_DBIAS                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_MAC                                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_MAC_EXT                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_OCODE                                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_OPTIONAL_UNIQUE_ID                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_PKG_VERSION                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
ESP_EFUSE_RD_DIS                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_RD_DIS_BLOCK_KEY0                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_RD_DIS_BLOCK_KEY1                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_RD_DIS_BLOCK_KEY2                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_RD_DIS_BLOCK_KEY3                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_RD_DIS_BLOCK_KEY4                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_RD_DIS_BLOCK_KEY5                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_SECURE_BOOT_DISABLE_FAST_WAKE           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_SECURE_BOOT_EN                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_SECURE_VERSION                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_SEC_DPA_LEVEL                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_SOFT_DIS_JTAG                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_SPI_BOOT_CRYPT_CNT                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_SPI_DOWNLOAD_MSPI_DIS                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_SWAP_UART_SDIO_EN                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_SYS_DATA_PART2                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_TEMP_CALIB                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_UART_PRINT_CONTROL                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
ESP_EFUSE_USB_EXCHG_PINS                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_USER_DATA                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_USER_DATA_MAC_CUSTOM                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_VDD_SPI_AS_GPIO                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WAFER_VERSION_MAJOR                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WAFER_VERSION_MINOR                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WDT_DELAY_SEL                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ACTIVE_HP_DBIAS                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ACTIVE_LP_DBIAS                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN1              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN2              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH0        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH1        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH2        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH3        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH4        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH5        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH6        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN1            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN2            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_BLK1                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_BLOCK_KEY0                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLOCK_KEY1                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLOCK_KEY2                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLOCK_KEY3                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLOCK_KEY4                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLOCK_KEY5                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_BLOCK_USR_DATA                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_CRYPT_DPA_ENABLE                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_CUSTOM_MAC                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DBIAS_VOL_GAP                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_ICACHE                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_WR_DIS_DIS_PAD_JTAG                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_TWAI                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_USB_JTAG                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DSLP_LP_DBG                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_DSLP_LP_DBIAS                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_FLASH_CAP                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_FLASH_TEMP                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_FLASH_TPUW                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_FLASH_VENDOR                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_0                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_1                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_2                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_3                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_4                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_5                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_LSLP_HP_DBG                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_LSLP_HP_DBIAS                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_MAC                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_MAC_EXT                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_OCODE                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_PKG_VERSION                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_RD_DIS                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SECURE_BOOT_EN                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_SECURE_VERSION                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SEC_DPA_LEVEL                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
ESP_EFUSE_WR_DIS_SPI_DOWNLOAD_MSPI_DIS            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SWAP_UART_SDIO_EN                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_SYS_DATA_PART1                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ESP_EFUSE_WR_DIS_TEMP_CALIB                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_USB_EXCHG_PINS                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_VDD_SPI_AS_GPIO                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
ESP_EFUSE_WR_DIS_WDT_DELAY_SEL                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_table.c.o)
LP_ANA_PERI                                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
LP_CLKRST                                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
LP_TIMER                                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
LP_UART                                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
LP_WDT                                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
MODEM_LPCON                                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
MODEM_SYSCON                                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
PCR                                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
PMU                                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
ROM_Boot_Cache_Init                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
SPIMEM0                                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
SPIMEM1                                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
TIMERG0                                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
TIMERG1                                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
UART0                                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
__ashldi3                                         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
__assert_func                                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
__clz_tab                                         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                                                  C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
__getreent                                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
__lshrdi3                                         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
__popcountsi2                                     C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
__sf                                              C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
__udivdi3                                         C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
_bss_end                                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
_bss_start                                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
_data_end                                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
_data_start                                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
_dram_end                                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
_dram_start                                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
_impure_data                                      C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
_impure_ptr                                       C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
abort                                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_after_init                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
bootloader_ana_clock_glitch_reset_config          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_ana_super_wdt_reset_config             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_soc.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_atexit                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_before_init                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
bootloader_check_bootloader_validity              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_clear_bss_section                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_clock_configure                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_common_check_chip_revision_validity    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
bootloader_common_check_chip_validity             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
bootloader_common_check_efuse_blk_validity        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
bootloader_common_get_active_otadata              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_common_get_partition_description       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_common_ota_select_crc                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_common_ota_select_invalid              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_common_ota_select_valid                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
bootloader_common_read_otadata                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_common_select_otadata                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
bootloader_config_wdt                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_configure_spi_pins                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_console_deinit                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_console_init                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_debug_buffer                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
bootloader_enable_random                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_enable_wp                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_execute_flash_command                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_fill_random                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
bootloader_flash_clock_config                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_flash_cs_timing_config                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_flash_erase_range                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
bootloader_flash_erase_sector                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_flash_execute_command_common           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
bootloader_flash_get_spi_mode                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_flash_is_octal_mode_enabled            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
bootloader_flash_read                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
bootloader_flash_read_sfdp                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
bootloader_flash_reset_chip                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
bootloader_flash_unlock                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_flash_unlock_default                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
bootloader_flash_update_id                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_flash_update_size                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_flash_write                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_flash_xmc_startup                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_image_hdr                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_init                                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
bootloader_init_mem                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_init_spi_flash                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_load_image                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_load_image_no_verify                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
bootloader_mmap                                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_mmap_get_free_pages                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_munmap                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_print_banner                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_random_disable                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_random_enable                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
bootloader_read_bootloader_header                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
bootloader_read_flash_id                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
bootloader_reset                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
bootloader_sha256_data                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_sha256_finish                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_sha256_flash_contents                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_sha256_hex_to_str                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_sha256_start                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
bootloader_spi_flash_reset                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
bootloader_utility_get_selected_boot_partition    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
bootloader_utility_load_boot_image                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
bootloader_utility_load_partition_table           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
cache_hal_disable                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
cache_hal_enable                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
cache_hal_freeze                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
cache_hal_get_cache_line_size                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
cache_hal_init                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
cache_hal_invalidate_addr                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
cache_hal_is_cache_enabled                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
cache_hal_resume                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
cache_hal_suspend                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
cache_hal_unfreeze                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
cache_hal_vaddr_to_cache_level_id                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
call_start_cpu0                                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
efuse_hal_blk_version                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
efuse_hal_chip_revision                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
efuse_hal_clear_program_registers                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
efuse_hal_flash_encryption_enabled                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
efuse_hal_get_chip_ver_pkg                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
efuse_hal_get_disable_blk_version_major           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
efuse_hal_get_disable_wafer_version_major         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
efuse_hal_get_mac                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
efuse_hal_get_major_chip_version                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
efuse_hal_get_minor_chip_version                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
efuse_hal_is_coding_error_in_block                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
efuse_hal_program                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
efuse_hal_read                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
efuse_hal_rs_calculate                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
efuse_hal_set_timing                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_bootloader_desc                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
esp_bootloader_get_description                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_bootloader_format\libesp_bootloader_format.a(esp_bootloader_desc.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
esp_cpu_configure_region_protection               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_mem.c.o)
esp_efuse_batch_write_begin                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_batch_write_cancel                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_batch_write_commit                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_block_is_empty                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_check_errors                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_count_unused_key_blocks                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_destroy_block                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_disable_rom_download_mode               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
esp_efuse_enable_rom_secure_download_mode         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_find_purpose                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_find_unused_key_block                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_get_coding_scheme                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_get_digest_revoke                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_get_field_size                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_get_key                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_get_key_dis_read                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_get_key_dis_write                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_get_key_purpose                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_get_keypurpose_dis_write                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_get_pkg_ver                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
esp_efuse_get_purpose_field                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_get_write_protect_of_digest_revoke      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_key_block_unused                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_read_block                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_read_field_bit                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_read_field_blob                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_read_field_cnt                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_read_reg                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_set_digest_revoke                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_set_key_dis_read                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_set_key_dis_write                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_set_key_purpose                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_set_keypurpose_dis_write                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_set_read_protect                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_set_rom_log_scheme                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
esp_efuse_set_write_protect                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_set_write_protect_of_digest_revoke      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_utility_apply_new_coding_scheme         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_burn_chip                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_burn_chip_opt                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_burn_efuses                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_check_errors                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_clear_program_registers         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_count_once                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_debug_dump_blocks               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_debug_dump_pending              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_debug_dump_single_block         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_erase_virt_blocks               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_fill_buff                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_get_number_of_items             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_get_read_register_address       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_utility_is_correct_written_data         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_process                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_read_reg                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_reset                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_update_virt_blocks              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
esp_efuse_utility_write_blob                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_write_cnt                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_utility_write_reg                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_write_block                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_efuse_write_field_bit                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_write_field_blob                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_fields.c.o)
esp_efuse_write_field_cnt                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_efuse_write_key                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_write_keys                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
esp_efuse_write_reg                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
esp_flash_encryption_cfg_verify_release_mode      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_flash_encryption_enabled                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
esp_flash_encryption_set_release_mode             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_flash_write_protect_crypt_cnt                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_get_flash_encryption_mode                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
esp_image_bootloader_offset_get                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
esp_image_bootloader_offset_set                   .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
esp_image_get_flash_size                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
esp_image_get_metadata                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
esp_image_verify                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
esp_image_verify_bootloader                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
esp_image_verify_bootloader_data                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
esp_log_early_timestamp                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
esp_log_timestamp                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
esp_partition_table_verify                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
esp_rom_crc32_le                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
esp_rom_delay_us                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
esp_rom_get_cpu_ticks_per_us                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\log\liblog.a(log_timestamp.c.o)
esp_rom_get_reset_reason                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
esp_rom_gpio_pad_set_drv                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
esp_rom_install_uart_printf                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
esp_rom_md5_final                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
esp_rom_md5_init                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
esp_rom_md5_update                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
esp_rom_output_flush_tx                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console_loader.c.o)
esp_rom_output_tx_wait_idle                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
esp_rom_printf                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_encrypt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_common_loader.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
esp_rom_regi2c_read                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
esp_rom_regi2c_read_mask                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
esp_rom_regi2c_write                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
esp_rom_regi2c_write_mask                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random_esp32c6.c.o)
esp_rom_set_cpu_ticks_per_us                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
esp_rom_software_reset_system                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
esp_rom_spiflash_config_clk                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
esp_rom_spiflash_config_param                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
esp_rom_spiflash_erase_block                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
esp_rom_spiflash_erase_sector                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
esp_rom_spiflash_fix_dummylen                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
esp_rom_spiflash_read                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
esp_rom_spiflash_wait_idle                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
esp_rom_spiflash_write                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
esp_rom_spiflash_write_encrypted                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
esp_secure_boot_read_key_digests                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
ets_efuse_clear_program_registers                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
ets_efuse_rs_calculate                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(efuse_hal.c.o)
ets_sha_enable                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
ets_sha_finish                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
ets_sha_init                                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
ets_sha_update                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
get_act_hp_dbias                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
get_act_lp_dbias                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
lp_timer_hal_clear_alarm_intr_status              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
lp_timer_hal_clear_overflow_intr_status           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
lp_timer_hal_get_cycle_count                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_random.c.o)
lp_timer_hal_set_alarm_target                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(lp_timer_hal.c.o)
memcmp                                            C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
memcpy                                            C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
memset                                            C:/Users/<USER>/.platformio/packages/toolchain-riscv32-esp/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_sha.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\main\libmain.a(bootloader_start.c.o)
mmu_hal_bytes_to_pages                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
mmu_hal_check_valid_ext_vaddr_region              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
mmu_hal_init                                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_esp32c6.c.o)
mmu_hal_map_region                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
mmu_hal_paddr_to_vaddr                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
mmu_hal_pages_to_bytes                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
mmu_hal_unmap_all                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_utility.c.o)
mmu_hal_unmap_region                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
mmu_hal_vaddr_to_paddr                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(mmu_hal.c.o)
pmu_hp_system_analog_param_default                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
pmu_hp_system_clock_param_default                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
pmu_hp_system_digital_param_default               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
pmu_hp_system_power_param_default                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
pmu_hp_system_retention_param_default             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
pmu_lp_system_analog_param_default                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
pmu_lp_system_power_param_default                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(pmu_param.c.o)
range_read_addr_blocks                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
range_write_addr_blocks                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_utility.c.o)
regi2c_read_impl                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
regi2c_read_mask_impl                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
regi2c_write_impl                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
regi2c_write_mask_impl                            .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.o)
rom_spiflash_legacy_data                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash_config_esp32c6.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_flash.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(flash_partitions.c.o)
rtc_clk_32k_bootstrap                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_32k_disable_external                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_32k_enable                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_32k_enable_external                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_32k_enabled                               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_8m_enable                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_8m_enabled                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_apb_freq_get                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
rtc_clk_bbpll_add_consumer                        .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_bbpll_remove_consumer                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_cpu_freq_get_config                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_cpu_freq_mhz_to_config                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_cpu_freq_set_config                       .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_cpu_freq_set_config_fast                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_cpu_freq_set_xtal                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_cpu_freq_set_xtal_for_sleep               .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_cpu_freq_to_pll_and_pll_lock_release      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_cpu_set_to_default_config                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_fast_src_get                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
rtc_clk_fast_src_set                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_init                                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
rtc_clk_rc32k_enable                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_set_cpu_switch_to_pll                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_clk_slow_freq_get_hz                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
rtc_clk_slow_src_get                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_clock_init.c.o)
rtc_clk_slow_src_set                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_clk_xtal_freq_get                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_console.c.o)
rtc_clk_xtal_freq_update                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk_init.c.o)
rtc_dig_8m_enabled                                .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_dig_clk8m_disable                             .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_dig_clk8m_enable                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rtc_get_xtal                                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(rtc_clk.c.o)
rv_utils_dbgr_is_attached                         .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\riscv\libriscv.a(rv_utils.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_hw_support\libesp_hw_support.a(cpu_region_protect.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_panic.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(esp_image_format.c.o)
s_cache_hal_init_ctx                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
s_get_cache_state                                 .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
s_revoke_table                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
s_table                                           .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\efuse\libefuse.a(esp_efuse_api_key.c.o)
s_update_cache_state                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\hal\libhal.a(cache_hal.c.o)
wdt_hal_config_stage                              .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
wdt_hal_deinit                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
wdt_hal_enable                                    .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
wdt_hal_init                                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\esp_rom\libesp_rom.a(esp_rom_wdt.c.o)
                                                  .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
wdt_hal_set_flashboot_en                          .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
wdt_hal_write_protect_disable                     .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
wdt_hal_write_protect_enable                      .pio\build\esp32-c6-devkitc-1\bootloader\esp-idf\bootloader_support\libbootloader_support.a(bootloader_init.c.o)
