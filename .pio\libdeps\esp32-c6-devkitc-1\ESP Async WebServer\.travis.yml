sudo: false

language: python

os:
  - linux

git:
  depth: false

stages:
  - build

jobs:
  include:

    - name: "Build Arduino ESP32"
      if: tag IS blank AND (type = pull_request OR (type = push AND branch = master))
      stage: build
      script: bash $TRAVIS_BUILD_DIR/.github/scripts/on-push.sh esp32

    - name: "Build Arduino ESP8266"
      if: tag IS blank AND (type = pull_request OR (type = push AND branch = master))
      stage: build
      script: bash $TRAVIS_BUILD_DIR/.github/scripts/on-push.sh esp8266

    - name: "Build Platformio ESP32"
      if: tag IS blank AND (type = pull_request OR (type = push AND branch = master))
      stage: build
      script: bash $TRAVIS_BUILD_DIR/.github/scripts/on-push.sh esp32 1 1

    - name: "Build Platformio ESP8266"
      if: tag IS blank AND (type = pull_request OR (type = push AND branch = master))
      stage: build
      script: bash $TRAVIS_BUILD_DIR/.github/scripts/on-push.sh esp8266 1 1

notifications:
  email:
    on_success: change
    on_failure: change
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/60e65d0c78ea0a920347
    on_success: change  # options: [always|never|change] default: always
    on_failure: always  # options: [always|never|change] default: always
    on_start: never     # options: [always|never|change] default: always
