-o .pio/build/esp32-c6-devkitc-1/src/main.c.o -c -Og -Wall -Werror=all -Wextra -Wno-enum-conversion -Wno-error=deprecated-declarations -Wno-error=extra -Wno-error=unused-but-set-variable -Wno-error=unused-function -Wno-error=unused-variable -Wno-old-style-declaration -Wno-sign-compare -Wno-unused-parameter -fdata-sections -fdiagnostics-color=always -ffunction-sections -fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf=/IDF -fmacro-prefix-map=C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver=. -fno-jump-tables -fno-shrink-wrap -fno-tree-switch-conversion -fstrict-volatile-bitfields -gdwarf-4 -ggdb -march=rv32imac_zicsr_zifencei -nostartfiles -std=gnu17 -march=rv32imac_zicsr_zifencei -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver=. -fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -Wno-old-style-declaration -D_POSIX_READER_WRITER_LOCKS -D_GNU_SOURCE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DIDF_VER=\"5.5.0\" -DESP_PLATFORM -DPLATFORMIO=60118 -DCORE_DEBUG_LEVEL=3 -DCONFIG_ARDUHAL_LOG_COLORS=1 -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/riscv/include -I.pio/build/esp32-c6-devkitc-1/config -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/platform_include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include/freertos -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/riscv/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/riscv/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/riscv/include/freertos -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/esp_additions/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc/esp32c6 -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/dma/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/ldo/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/debug_probe/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/power_supply/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6 -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/private_include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/heap/tlsf -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/log/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6 -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/register -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/platform_port/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/esp32c6/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include/esp32c6 -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6 -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/soc -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/include/riscv -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/include/private -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps/sntp -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/lwip/src/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/freertos/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/arch -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/sys -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_gpio/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_timer/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_pm/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/port/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/library -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/esp_crt_bundle/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_app_format/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_bootloader_format/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/app_update/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_partition/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/esp32c6/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_mm/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_security/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/pthread/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_gptimer/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_ringbuf/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_uart/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/vfs/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/app_trace/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_event/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/nvs_flash/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_phy/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_phy/esp32c6/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_usb_serial_jtag/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_vfs_console/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_netif/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/wpa_supplicant/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/wpa_supplicant/port/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/wpa_supplicant/esp_supplicant/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_coex/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/include/local -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/wifi_apps/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/wifi_apps/nan_app/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_spi/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_gdbstub/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/unity/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/unity/unity/src -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/cmock/CMock/src -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/console -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_pcnt/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_mcpwm/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ana_cmpr/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_i2s/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/sdmmc/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdmmc/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdspi/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdio/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_dac/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_bitscrambler/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_rmt/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_tsens/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdm/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_i2c/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ledc/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_parlio/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_twai/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/driver/deprecated -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/driver/i2c/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/driver/touch_sensor/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/driver/twai/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/http_parser -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp-tls -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp-tls/esp-tls-crypto -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/interface -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/esp32c6/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/deprecated/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_isp/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_cam/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_cam/interface -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_psram/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_jpeg/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ppa/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_eth/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hid/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/tcp_transport/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_http_client/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_http_server/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_https_ota/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_https_server/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_lcd/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_lcd/interface -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/protobuf-c/protobuf-c -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/common -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/security -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/transports -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/crypto/srp6a -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/proto-c -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_tee/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/espcoredump/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/espcoredump/include/port/riscv -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/wear_levelling/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/fatfs/diskio -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/fatfs/src -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/fatfs/vfs -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/idf_test/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/idf_test/include/esp32c6 -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/ieee802154/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/json/cJSON -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/mqtt/esp-mqtt/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/nvs_sec_provider/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/rt/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/spiffs/include -IC:/Users/<USER>/.platformio/packages/framework-espidf/components/wifi_provisioning/include -Isrc -Iinclude -Isrc -I. src/main.c
