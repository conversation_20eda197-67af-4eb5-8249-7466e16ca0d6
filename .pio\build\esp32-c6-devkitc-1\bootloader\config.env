{"COMPONENT_KCONFIGS": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_security/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/log/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/Kconfig;C:/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader/Kconfig.projbuild;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_app_format/Kconfig.projbuild;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/Kconfig.projbuild;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_tee/Kconfig.projbuild;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py/Kconfig.projbuild;C:/Users/<USER>/.platformio/packages/framework-espidf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader/sdkconfig.rename;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/sdkconfig.rename;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/sdkconfig.rename;C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py/sdkconfig.rename;C:/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/sdkconfig.rename;C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/sdkconfig.rename;C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/sdkconfig.rename;C:/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32c6", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.5.0", "IDF_ENV_FPGA": "", "IDF_PATH": "C:/Users/<USER>/.platformio/packages/framework-espidf", "COMPONENT_KCONFIGS_SOURCE_FILE": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/kconfigs_projbuild.in"}