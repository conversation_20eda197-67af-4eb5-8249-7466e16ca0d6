<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Environmental Monitor</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Navigation -->
        <nav class="sidebar">
            <div class="logo">
                <i class="fas fa-thermometer-half"></i>
                <span>ESP32 Monitor</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </li>
                <li class="nav-item" data-page="sensors">
                    <i class="fas fa-microchip"></i>
                    <span>Sensors</span>
                </li>
                <li class="nav-item" data-page="air-quality">
                    <i class="fas fa-wind"></i>
                    <span>Air Quality</span>
                </li>
                <li class="nav-item" data-page="environment">
                    <i class="fas fa-leaf"></i>
                    <span>Environment</span>
                </li>
                <li class="nav-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h1 id="page-title">Dashboard</h1>
                    <div class="breadcrumb">
                        <span id="current-time"></span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="status-indicator">
                        <div class="status-dot online" id="connection-status"></div>
                        <span>Online</span>
                    </div>
                </div>
            </header>

            <!-- Dashboard Page -->
            <div class="page active" id="dashboard-page">
                <div class="dashboard-grid">
                    <!-- System Status Card -->
                    <div class="card system-status">
                        <div class="card-header">
                            <h3>System Status</h3>
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="card-content">
                            <div class="status-grid">
                                <div class="status-item">
                                    <span class="label">Uptime</span>
                                    <span class="value" id="uptime">--</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">Sensors Online</span>
                                    <span class="value" id="sensors-online">--</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">Data Logging</span>
                                    <span class="value status-enabled" id="logging-status">Enabled</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">WiFi Signal</span>
                                    <span class="value" id="wifi-signal">--</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Temperature Card -->
                    <div class="card temperature-card">
                        <div class="card-header">
                            <h3>Temperature</h3>
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div class="card-content">
                            <div class="gauge-container">
                                <canvas id="temperature-gauge"></canvas>
                                <div class="gauge-value">
                                    <span id="temperature-value">--</span>
                                    <span class="unit">°C</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Humidity Card -->
                    <div class="card humidity-card">
                        <div class="card-header">
                            <h3>Humidity</h3>
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="card-content">
                            <div class="gauge-container">
                                <canvas id="humidity-gauge"></canvas>
                                <div class="gauge-value">
                                    <span id="humidity-value">--</span>
                                    <span class="unit">%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Air Quality Card -->
                    <div class="card air-quality-card">
                        <div class="card-header">
                            <h3>Air Quality</h3>
                            <i class="fas fa-wind"></i>
                        </div>
                        <div class="card-content">
                            <div class="air-quality-display">
                                <div class="aqi-circle" id="aqi-circle">
                                    <span id="aqi-value">--</span>
                                </div>
                                <div class="aqi-status">
                                    <span id="aqi-status">Good</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Data Chart -->
                    <div class="card chart-card">
                        <div class="card-header">
                            <h3>Recent Readings</h3>
                            <div class="chart-controls">
                                <select id="chart-timeframe">
                                    <option value="1h">Last Hour</option>
                                    <option value="6h">Last 6 Hours</option>
                                    <option value="24h" selected>Last 24 Hours</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-content">
                            <canvas id="recent-data-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other pages will be added here -->
            <div class="page" id="sensors-page">
                <div class="sensors-content">
                    <h2>Sensor Management</h2>
                    <p>Sensor management interface will be implemented here.</p>
                </div>
            </div>

            <div class="page" id="air-quality-page">
                <div class="air-quality-content">
                    <h2>Air Quality Monitoring</h2>
                    <p>Air quality charts and data will be displayed here.</p>
                </div>
            </div>

            <div class="page" id="environment-page">
                <div class="environment-content">
                    <h2>Environmental Data</h2>
                    <p>Temperature, humidity, and pressure data will be shown here.</p>
                </div>
            </div>

            <div class="page" id="settings-page">
                <div class="settings-content">
                    <h2>Settings</h2>
                    <p>System settings and configuration options will be available here.</p>
                </div>
            </div>
        </main>
    </div>

    <script src="js/app.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/api.js"></script>
</body>
</html>
