name: "Close stale issues and PRs"
on:
  schedule:
    - cron: "30 1 * * *"

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          stale-issue-message: "This issue is stale because it has been open 30 days with no activity. Remove stale label or comment or this will be closed in 7 days."
          stale-pr-message: "This PR is stale because it has been open 30 days with no activity. Remove stale label or comment or this will be closed in 7 days."
          close-issue-message: "This issue was closed because it has been stalled for 7 days with no activity."
          close-pr-message: "This PR was closed because it has been stalled for 7 days with no activity."
          days-before-issue-stale: 30
          days-before-pr-stale: 30
          days-before-issue-close: 7
          days-before-pr-close: 7

permissions:
  # contents: write # only for delete-branch option
  issues: write
  pull-requests: write
