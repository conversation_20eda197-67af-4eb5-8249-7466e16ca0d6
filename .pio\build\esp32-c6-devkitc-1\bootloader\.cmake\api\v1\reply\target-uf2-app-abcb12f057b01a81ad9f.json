{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "project"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 72, "parent": 0}, {"command": 0, "file": 0, "line": 962, "parent": 1}]}, "id": "uf2-app::@6890427a1f51a3e7e1df", "name": "uf2-app", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/CMakeFiles/uf2-app", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/CMakeFiles/uf2-app.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}