/* Modern CSS Variables for theming */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
}

/* App container */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
  width: 260px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 2rem 0;
  box-shadow: var(--shadow-lg);
  position: fixed;
  height: 100vh;
  z-index: 1000;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0 2rem;
  margin-bottom: 3rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.logo i {
  font-size: 2rem;
  color: var(--accent-color);
}

.nav-menu {
  list-style: none;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  cursor: pointer;
  transition: var(--transition);
  border-left: 4px solid transparent;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: var(--accent-color);
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.15);
  border-left-color: var(--accent-color);
}

.nav-item i {
  font-size: 1.25rem;
  width: 1.5rem;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 260px;
  background-color: var(--background-color);
}

/* Header */
.header {
  background: var(--surface-color);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.breadcrumb {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--success-color);
  color: white;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Pages */
.page {
  display: none;
  padding: 2rem;
  animation: fadeIn 0.5s ease-in-out;
}

.page.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1400px;
}

/* Cards */
.card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-header i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.card-content {
  padding: 1.5rem;
}

/* System Status Card */
.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.status-item .label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.status-item .value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.status-enabled {
  color: var(--success-color) !important;
}

/* Gauge Container */
.gauge-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.gauge-value {
  position: absolute;
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.gauge-value .unit {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 400;
}

/* Air Quality Display */
.air-quality-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.aqi-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, var(--success-color) 0%, var(--accent-color) 100%);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.aqi-status {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--success-color);
}

/* Chart Card */
.chart-card {
  grid-column: 1 / -1;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-controls select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--surface-color);
  color: var(--text-primary);
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: var(--transition);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .header {
    padding: 1rem;
  }
  
  .page {
    padding: 1rem;
  }
}
