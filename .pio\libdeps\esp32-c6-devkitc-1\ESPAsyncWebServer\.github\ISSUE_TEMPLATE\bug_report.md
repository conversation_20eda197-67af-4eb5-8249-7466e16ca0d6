---
name: Bug report
about: Only for bugs founds in the library! Otherwise, please go to the discussions section!
title: "[BUG]"
labels: triage
assignees: ""
---

**Only for bugs found in the library! Otherwise, please go to the [discussions section](https://github.com/ESP32Async/ESPAsyncWebServer/discussions)!**

**Please make sure to go through the recommendations before opening a bug report:**

[https://github.com/ESP32Async/ESPAsyncWebServer?tab=readme-ov-file#important-recommendations](https://github.com/ESP32Async/ESPAsyncWebServer?tab=readme-ov-file#important-recommendations)

**Description**

_A clear and concise description of what the bug is._

**Link:** _A link to your source code where the issue happens_

**Board**: _esp32dev, esp32s3, ?_

**Ethernet adapter used ?** _If yes, please specify which one_

**Stack trace**

Please provide a **decoded stack trace** taken with `monitor_filters = esp32_exception_decoder`.
**Any issue opened with a non readable stack trace will be ignored because not helpful at all.**

As an alternative, you can use [https://maximeborges.github.io/esp-stacktrace-decoder/](https://maximeborges.github.io/esp-stacktrace-decoder/).

**Additional notes**

Add any other context about the problem here.

# WARNING

**ANY ISSUE POSTED WITH AN ENCODED STACK TRACE WILL BE CLOSED !**

**PLEASE MAKE THE EFFORT TO DECODE YOUR STACK TRACE**
