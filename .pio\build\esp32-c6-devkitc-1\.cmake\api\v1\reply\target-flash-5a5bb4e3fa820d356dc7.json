{"backtrace": 8, "backtraceGraph": {"commands": ["add_custom_target", "esptool_py_flash_target", "esptool_py_custom_target", "include", "__build_process_project_includes", "idf_build_process", "project", "add_dependencies"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py/project_include.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/build.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 6, "file": 3, "line": 6, "parent": 0}, {"command": 5, "file": 2, "line": 740, "parent": 1}, {"command": 4, "file": 1, "line": 718, "parent": 2}, {"command": 3, "file": 1, "line": 467, "parent": 3}, {"file": 0, "parent": 4}, {"command": 2, "file": 0, "line": 547, "parent": 5}, {"command": 1, "file": 0, "line": 516, "parent": 6}, {"command": 0, "file": 0, "line": 404, "parent": 7}, {"command": 7, "file": 0, "line": 522, "parent": 6}]}, "dependencies": [{"backtrace": 9, "id": "partition_table_bin::@8e7a60ee4fe55e1aaabf"}, {"backtrace": 9, "id": "bootloader::@6890427a1f51a3e7e1df"}, {"backtrace": 9, "id": "app::@6890427a1f51a3e7e1df"}], "id": "flash::@6890427a1f51a3e7e1df", "name": "flash", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 8, "isGenerated": true, "path": ".pio/build/esp32-c6-devkitc-1/CMakeFiles/flash", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": ".pio/build/esp32-c6-devkitc-1/CMakeFiles/flash.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}