{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "partition_table_add_check_bootloader_size_target"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/components/partition_table/project_include.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 76, "parent": 0}, {"command": 0, "file": 0, "line": 140, "parent": 1}]}, "dependencies": [{"id": "gen_project_binary::@6890427a1f51a3e7e1df"}], "id": "bootloader_check_size::@bf8f55be585a12323665", "name": "bootloader_check_size", "paths": {"build": "esp-idf/esptool_py", "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esptool_py"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}