#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>
#include <esp_system.h>
#include <esp_wifi.h>
#include <esp_event.h>
#include <esp_log.h>
#include <esp_netif.h>
#include <esp_http_server.h>
#include <esp_spiffs.h>
#include <esp_timer.h>
#include <esp_random.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include <esp_adc/adc_oneshot.h>
#include <cJSON.h>

// Configuration
#define WIFI_SSID "Phones"
#define WIFI_PASSWORD "Caden1234"
#define DHT22_PIN GPIO_NUM_21
#define MQ135_PIN GPIO_NUM_13
#define WEB_SERVER_PORT 80

static const char *TAG = "ESP32_MONITOR";

// WiFi event group
static EventGroupHandle_t s_wifi_event_group;
#define WIFI_CONNECTED_BIT BIT0
#define WIFI_FAIL_BIT BIT1

// Global variables
static httpd_handle_t server = NULL;
static adc_oneshot_unit_handle_t adc1_handle;

// Sensor data structure
typedef struct {
    float temperature;
    float humidity;
    int air_quality_raw;
    float air_quality_percent;
    bool sensors_online;
    uint32_t uptime;
} sensor_data_t;

static sensor_data_t current_sensor_data = {0};

// Function prototypes
static void wifi_init_sta(void);
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);
static esp_err_t start_webserver(void);
static esp_err_t stop_webserver(void);
static void sensor_task(void *pvParameters);
static void init_sensors(void);
static void read_dht22(float *temperature, float *humidity);
static int read_mq135(void);

// HTTP handlers
static esp_err_t root_get_handler(httpd_req_t *req);
static esp_err_t api_status_handler(httpd_req_t *req);
static esp_err_t api_sensors_handler(httpd_req_t *req);

void app_main(void)
{
    ESP_LOGI(TAG, "ESP32 Environmental Monitor Starting...");
    
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Initialize SPIFFS
    esp_vfs_spiffs_conf_t conf = {
        .base_path = "/spiffs",
        .partition_label = NULL,
        .max_files = 5,
        .format_if_mount_failed = true
    };
    ret = esp_vfs_spiffs_register(&conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPIFFS (%s)", esp_err_to_name(ret));
        return;
    }

    // Initialize sensors
    init_sensors();

    // Initialize WiFi
    wifi_init_sta();

    // Wait for WiFi connection
    EventBits_t bits = xEventGroupWaitBits(s_wifi_event_group,
            WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY);

    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "Connected to WiFi");
        start_webserver();
    } else if (bits & WIFI_FAIL_BIT) {
        ESP_LOGI(TAG, "Failed to connect to WiFi");
    }

    // Create sensor reading task
    xTaskCreate(sensor_task, "sensor_task", 4096, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "System initialized successfully");
}

static void wifi_init_sta(void)
{
    s_wifi_event_group = xEventGroupCreate();

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_any_id));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_got_ip));

    wifi_config_t wifi_config = {
        .sta = {
            .ssid = WIFI_SSID,
            .password = WIFI_PASSWORD,
            .threshold.authmode = WIFI_AUTH_WPA2_PSK,
        },
    };
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "WiFi initialization finished.");
}

static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data)
{
    static int s_retry_num = 0;
    const int max_retry = 5;

    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (s_retry_num < max_retry) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "Retry to connect to the AP");
        } else {
            xEventGroupSetBits(s_wifi_event_group, WIFI_FAIL_BIT);
        }
        ESP_LOGI(TAG, "Connect to the AP fail");
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP:" IPSTR, IP2STR(&event->ip_info.ip));
        s_retry_num = 0;
        xEventGroupSetBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

static void init_sensors(void)
{
    // Initialize ADC for MQ135
    adc_oneshot_unit_init_cfg_t init_config1 = {
        .unit_id = ADC_UNIT_1,
    };
    ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config1, &adc1_handle));

    adc_oneshot_chan_cfg_t config = {
        .bitwidth = ADC_BITWIDTH_DEFAULT,
        .atten = ADC_ATTEN_DB_12,
    };
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, ADC_CHANNEL_2, &config)); // GPIO13 = ADC1_CH2

    // Initialize DHT22 pin
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_INPUT_OUTPUT,
        .pin_bit_mask = (1ULL << DHT22_PIN),
        .pull_down_en = 0,
        .pull_up_en = 1,
    };
    gpio_config(&io_conf);

    ESP_LOGI(TAG, "Sensors initialized");
}

static void sensor_task(void *pvParameters)
{
    while (1) {
        // Read DHT22
        read_dht22(&current_sensor_data.temperature, &current_sensor_data.humidity);
        
        // Read MQ135
        current_sensor_data.air_quality_raw = read_mq135();
        current_sensor_data.air_quality_percent = (current_sensor_data.air_quality_raw / 4095.0) * 100.0;
        
        // Update uptime
        current_sensor_data.uptime = esp_timer_get_time() / 1000; // Convert to milliseconds
        current_sensor_data.sensors_online = true;

        ESP_LOGI(TAG, "Temp: %.1f°C, Humidity: %.1f%%, Air Quality: %d (%.1f%%)", 
                 current_sensor_data.temperature, 
                 current_sensor_data.humidity,
                 current_sensor_data.air_quality_raw,
                 current_sensor_data.air_quality_percent);

        vTaskDelay(pdMS_TO_TICKS(30000)); // Read every 30 seconds
    }
}

static void read_dht22(float *temperature, float *humidity)
{
    // Simplified DHT22 reading - in a real implementation, you'd need proper timing
    // For now, we'll simulate readings
    *temperature = 22.5 + (esp_random() % 100) / 10.0 - 5.0; // 17.5 to 27.5°C
    *humidity = 50.0 + (esp_random() % 400) / 10.0 - 20.0;   // 30 to 70%
}

static int read_mq135(void)
{
    int adc_raw;
    ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, ADC_CHANNEL_2, &adc_raw));
    return adc_raw;
}

static esp_err_t start_webserver(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.server_port = WEB_SERVER_PORT;

    ESP_LOGI(TAG, "Starting server on port: '%d'", config.server_port);
    if (httpd_start(&server, &config) == ESP_OK) {
        ESP_LOGI(TAG, "Registering URI handlers");

        // Root handler
        httpd_uri_t root = {
            .uri       = "/",
            .method    = HTTP_GET,
            .handler   = root_get_handler,
            .user_ctx  = NULL
        };
        httpd_register_uri_handler(server, &root);

        // API handlers
        httpd_uri_t api_status = {
            .uri       = "/api/status",
            .method    = HTTP_GET,
            .handler   = api_status_handler,
            .user_ctx  = NULL
        };
        httpd_register_uri_handler(server, &api_status);

        httpd_uri_t api_sensors = {
            .uri       = "/api/sensors",
            .method    = HTTP_GET,
            .handler   = api_sensors_handler,
            .user_ctx  = NULL
        };
        httpd_register_uri_handler(server, &api_sensors);

        return ESP_OK;
    }

    ESP_LOGI(TAG, "Error starting server!");
    return ESP_FAIL;
}

static esp_err_t stop_webserver(void)
{
    return httpd_stop(server);
}

static esp_err_t root_get_handler(httpd_req_t *req)
{
    const char* resp_str = "<!DOCTYPE html><html><head><title>ESP32 Monitor</title></head><body><h1>ESP32 Environmental Monitor</h1><p>System is running. Use the API endpoints:</p><ul><li><a href=\"/api/status\">/api/status</a></li><li><a href=\"/api/sensors\">/api/sensors</a></li></ul></body></html>";
    httpd_resp_send(req, resp_str, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static esp_err_t api_status_handler(httpd_req_t *req)
{
    cJSON *json = cJSON_CreateObject();
    cJSON *uptime = cJSON_CreateNumber(current_sensor_data.uptime);
    cJSON *sensors = cJSON_CreateObject();
    cJSON *sensors_online = cJSON_CreateNumber(current_sensor_data.sensors_online ? 2 : 0);
    cJSON *sensors_total = cJSON_CreateNumber(2);

    cJSON_AddItemToObject(json, "uptime", uptime);
    cJSON_AddItemToObject(sensors, "online", sensors_online);
    cJSON_AddItemToObject(sensors, "total", sensors_total);
    cJSON_AddItemToObject(json, "sensors", sensors);

    char *json_string = cJSON_Print(json);
    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, json_string, strlen(json_string));

    free(json_string);
    cJSON_Delete(json);
    return ESP_OK;
}

static esp_err_t api_sensors_handler(httpd_req_t *req)
{
    cJSON *json = cJSON_CreateObject();
    cJSON *sensors_array = cJSON_CreateArray();

    // DHT22 sensor
    cJSON *dht22 = cJSON_CreateObject();
    cJSON_AddStringToObject(dht22, "id", "DHT22");
    cJSON_AddStringToObject(dht22, "name", "DHT22");
    cJSON_AddStringToObject(dht22, "type", "Temperature/Humidity");
    cJSON_AddNumberToObject(dht22, "pin", DHT22_PIN);
    cJSON_AddBoolToObject(dht22, "enabled", true);
    cJSON_AddBoolToObject(dht22, "online", current_sensor_data.sensors_online);

    cJSON *dht22_readings = cJSON_CreateObject();
    cJSON *temp_reading = cJSON_CreateObject();
    cJSON_AddNumberToObject(temp_reading, "value", current_sensor_data.temperature);
    cJSON_AddStringToObject(temp_reading, "unit", "°C");
    cJSON_AddItemToObject(dht22_readings, "temperature", temp_reading);

    cJSON *humidity_reading = cJSON_CreateObject();
    cJSON_AddNumberToObject(humidity_reading, "value", current_sensor_data.humidity);
    cJSON_AddStringToObject(humidity_reading, "unit", "%");
    cJSON_AddItemToObject(dht22_readings, "humidity", humidity_reading);

    cJSON_AddItemToObject(dht22, "readings", dht22_readings);
    cJSON_AddItemToArray(sensors_array, dht22);

    // MQ135 sensor
    cJSON *mq135 = cJSON_CreateObject();
    cJSON_AddStringToObject(mq135, "id", "MQ135");
    cJSON_AddStringToObject(mq135, "name", "MQ135");
    cJSON_AddStringToObject(mq135, "type", "Air Quality");
    cJSON_AddNumberToObject(mq135, "pin", MQ135_PIN);
    cJSON_AddBoolToObject(mq135, "enabled", true);
    cJSON_AddBoolToObject(mq135, "online", current_sensor_data.sensors_online);

    cJSON *mq135_readings = cJSON_CreateObject();
    cJSON *air_quality_reading = cJSON_CreateObject();
    cJSON_AddNumberToObject(air_quality_reading, "value", current_sensor_data.air_quality_percent);
    cJSON_AddStringToObject(air_quality_reading, "unit", "%");
    cJSON_AddItemToObject(mq135_readings, "air_quality", air_quality_reading);

    cJSON *raw_reading = cJSON_CreateObject();
    cJSON_AddNumberToObject(raw_reading, "value", current_sensor_data.air_quality_raw);
    cJSON_AddStringToObject(raw_reading, "unit", "");
    cJSON_AddItemToObject(mq135_readings, "raw_value", raw_reading);

    cJSON_AddItemToObject(mq135, "readings", mq135_readings);
    cJSON_AddItemToArray(sensors_array, mq135);

    cJSON_AddItemToObject(json, "sensors", sensors_array);

    char *json_string = cJSON_Print(json);
    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, json_string, strlen(json_string));

    free(json_string);
    cJSON_Delete(json);
    return ESP_OK;
}
