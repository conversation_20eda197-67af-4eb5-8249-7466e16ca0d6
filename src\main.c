#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_netif.h"
#include "esp_http_server.h"
#include "esp_spiffs.h"
#include "esp_timer.h"
#include "esp_random.h"
#include "nvs_flash.h"
#include "driver/gpio.h"
#include "esp_adc/adc_oneshot.h"

// Configuration
#define WIFI_SSID "Phones"
#define WIFI_PASSWORD "Caden1234"
#define DHT22_PIN GPIO_NUM_21
#define MQ135_PIN GPIO_NUM_13
#define WEB_SERVER_PORT 80

static const char *TAG = "ESP32_MONITOR";

// WiFi event group
static EventGroupHandle_t s_wifi_event_group;
#define WIFI_CONNECTED_BIT BIT0
#define WIFI_FAIL_BIT BIT1

// Global variables
static httpd_handle_t server = NULL;
static adc_oneshot_unit_handle_t adc1_handle;

// Sensor data structure
typedef struct {
    float temperature;
    float humidity;
    int air_quality_raw;
    float air_quality_percent;
    bool sensors_online;
    uint32_t uptime;
} sensor_data_t;

static sensor_data_t current_sensor_data = {0};

// Function prototypes
static void wifi_init_sta(void);
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);
static esp_err_t start_webserver(void);
static esp_err_t stop_webserver(void);
static void sensor_task(void *pvParameters);
static void init_sensors(void);
static void read_dht22(float *temperature, float *humidity);
static int read_mq135(void);

// HTTP handlers
static esp_err_t root_get_handler(httpd_req_t *req);
static esp_err_t favicon_handler(httpd_req_t *req);
static esp_err_t api_status_handler(httpd_req_t *req);
static esp_err_t api_sensors_handler(httpd_req_t *req);

void app_main(void)
{
    ESP_LOGI(TAG, "ESP32 Environmental Monitor Starting...");
    
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Initialize SPIFFS
    esp_vfs_spiffs_conf_t conf = {
        .base_path = "/spiffs",
        .partition_label = NULL,
        .max_files = 5,
        .format_if_mount_failed = true
    };
    ret = esp_vfs_spiffs_register(&conf);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to initialize SPIFFS (%s), continuing without file system", esp_err_to_name(ret));
        // Continue without SPIFFS for now
    } else {
        ESP_LOGI(TAG, "SPIFFS initialized successfully");
    }

    // Initialize sensors
    init_sensors();

    // Initialize WiFi
    wifi_init_sta();

    // Wait for WiFi connection
    EventBits_t bits = xEventGroupWaitBits(s_wifi_event_group,
            WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY);

    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "Connected to WiFi");
        start_webserver();
    } else if (bits & WIFI_FAIL_BIT) {
        ESP_LOGI(TAG, "Failed to connect to WiFi");
    }

    // Create sensor reading task
    xTaskCreate(sensor_task, "sensor_task", 4096, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "System initialized successfully");
}

static void wifi_init_sta(void)
{
    s_wifi_event_group = xEventGroupCreate();

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_any_id));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_got_ip));

    wifi_config_t wifi_config = {
        .sta = {
            .ssid = WIFI_SSID,
            .password = WIFI_PASSWORD,
            .threshold.authmode = WIFI_AUTH_WPA2_PSK,
        },
    };
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "WiFi initialization finished.");
}

static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data)
{
    static int s_retry_num = 0;
    const int max_retry = 5;

    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (s_retry_num < max_retry) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "Retry to connect to the AP");
        } else {
            xEventGroupSetBits(s_wifi_event_group, WIFI_FAIL_BIT);
        }
        ESP_LOGI(TAG, "Connect to the AP fail");
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP:" IPSTR, IP2STR(&event->ip_info.ip));
        s_retry_num = 0;
        xEventGroupSetBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

static void init_sensors(void)
{
    // Initialize ADC for MQ135
    adc_oneshot_unit_init_cfg_t init_config1 = {
        .unit_id = ADC_UNIT_1,
    };
    ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config1, &adc1_handle));

    adc_oneshot_chan_cfg_t config = {
        .bitwidth = ADC_BITWIDTH_DEFAULT,
        .atten = ADC_ATTEN_DB_12,
    };
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, ADC_CHANNEL_2, &config)); // GPIO13 = ADC1_CH2

    // Initialize DHT22 pin
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_INPUT_OUTPUT,
        .pin_bit_mask = (1ULL << DHT22_PIN),
        .pull_down_en = 0,
        .pull_up_en = 1,
    };
    gpio_config(&io_conf);

    ESP_LOGI(TAG, "Sensors initialized");
}

static void sensor_task(void *pvParameters)
{
    while (1) {
        // Read DHT22
        read_dht22(&current_sensor_data.temperature, &current_sensor_data.humidity);
        
        // Read MQ135
        current_sensor_data.air_quality_raw = read_mq135();
        current_sensor_data.air_quality_percent = (current_sensor_data.air_quality_raw / 4095.0) * 100.0;
        
        // Update uptime
        current_sensor_data.uptime = esp_timer_get_time() / 1000; // Convert to milliseconds
        current_sensor_data.sensors_online = true;

        ESP_LOGI(TAG, "Temp: %.1f°C, Humidity: %.1f%%, Air Quality: %d (%.1f%%)", 
                 current_sensor_data.temperature, 
                 current_sensor_data.humidity,
                 current_sensor_data.air_quality_raw,
                 current_sensor_data.air_quality_percent);

        vTaskDelay(pdMS_TO_TICKS(30000)); // Read every 30 seconds
    }
}

static void read_dht22(float *temperature, float *humidity)
{
    // Simplified DHT22 reading - in a real implementation, you'd need proper timing
    // For now, we'll simulate readings
    *temperature = 22.5 + (esp_random() % 100) / 10.0 - 5.0; // 17.5 to 27.5°C
    *humidity = 50.0 + (esp_random() % 400) / 10.0 - 20.0;   // 30 to 70%
}

static int read_mq135(void)
{
    int adc_raw;
    ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, ADC_CHANNEL_2, &adc_raw));
    return adc_raw;
}

static esp_err_t start_webserver(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.server_port = WEB_SERVER_PORT;

    ESP_LOGI(TAG, "Starting server on port: '%d'", config.server_port);
    if (httpd_start(&server, &config) == ESP_OK) {
        ESP_LOGI(TAG, "Registering URI handlers");

        // Root handler
        httpd_uri_t root = {
            .uri       = "/",
            .method    = HTTP_GET,
            .handler   = root_get_handler,
            .user_ctx  = NULL
        };
        httpd_register_uri_handler(server, &root);

        // API handlers
        httpd_uri_t api_status = {
            .uri       = "/api/status",
            .method    = HTTP_GET,
            .handler   = api_status_handler,
            .user_ctx  = NULL
        };
        httpd_register_uri_handler(server, &api_status);

        httpd_uri_t api_sensors = {
            .uri       = "/api/sensors",
            .method    = HTTP_GET,
            .handler   = api_sensors_handler,
            .user_ctx  = NULL
        };
        httpd_register_uri_handler(server, &api_sensors);

        return ESP_OK;
    }

    ESP_LOGI(TAG, "Error starting server!");
    return ESP_FAIL;
}

static esp_err_t stop_webserver(void)
{
    return httpd_stop(server);
}

static esp_err_t favicon_handler(httpd_req_t *req)
{
    // Simple SVG favicon
    const char* favicon_svg =
        "<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'>"
        "<circle cx='16' cy='16' r='14' fill='#2563eb'/>"
        "<text x='16' y='20' text-anchor='middle' fill='white' font-size='16'>🌡️</text>"
        "</svg>";

    httpd_resp_set_type(req, "image/svg+xml");
    httpd_resp_send(req, favicon_svg, strlen(favicon_svg));
    return ESP_OK;
}

static esp_err_t root_get_handler(httpd_req_t *req)
{
    const char* resp_str =
        "<!DOCTYPE html>"
        "<html><head>"
        "<title>ESP32 Environmental Monitor</title>"
        "<meta name='viewport' content='width=device-width, initial-scale=1.0'>"
        "<link rel='icon' href='/favicon.ico' type='image/svg+xml'>"
        "<link href='https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' rel='stylesheet'>"
        "<style>"
        "*{box-sizing:border-box;}"
        "body{font-family:'Inter',system-ui,-apple-system,sans-serif;margin:0;padding:20px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;}"
        ".container{max-width:1000px;margin:0 auto;background:rgba(255,255,255,0.95);padding:30px;border-radius:20px;box-shadow:0 20px 40px rgba(0,0,0,0.1);backdrop-filter:blur(10px);}"
        "h1{color:#1e293b;text-align:center;margin-bottom:40px;font-size:2.5rem;font-weight:700;text-shadow:0 2px 4px rgba(0,0,0,0.1);}"
        ".status{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:25px;margin:30px 0;}"
        ".card{background:linear-gradient(145deg,#ffffff,#f8fafc);padding:25px;border-radius:15px;border:1px solid #e2e8f0;box-shadow:0 4px 15px rgba(0,0,0,0.08);transition:transform 0.3s ease,box-shadow 0.3s ease;}"
        ".card:hover{transform:translateY(-5px);box-shadow:0 8px 25px rgba(0,0,0,0.15);}"
        ".card h3{margin:0 0 15px 0;color:#334155;font-size:1.1rem;font-weight:600;display:flex;align-items:center;gap:8px;}"
        ".card-icon{font-size:1.5rem;}"
        ".value-group{margin:10px 0;}"
        ".value-primary{font-size:2rem;font-weight:700;color:#2563eb;margin-bottom:5px;}"
        ".value-secondary{font-size:1.1rem;color:#64748b;font-weight:500;}"
        ".unit-toggle{background:#f1f5f9;border:1px solid #cbd5e1;border-radius:20px;padding:4px;display:inline-flex;margin-top:10px;}"
        ".unit-btn{background:none;border:none;padding:6px 12px;border-radius:16px;font-size:0.85rem;cursor:pointer;transition:all 0.2s;color:#64748b;font-weight:500;}"
        ".unit-btn.active{background:#2563eb;color:white;box-shadow:0 2px 4px rgba(37,99,235,0.3);}"
        ".api-links{margin-top:40px;text-align:center;}"
        ".api-links h3{color:#334155;margin-bottom:20px;}"
        ".api-links a{display:inline-block;margin:8px;padding:12px 24px;background:linear-gradient(145deg,#2563eb,#1d4ed8);color:white;text-decoration:none;border-radius:25px;font-weight:500;transition:all 0.3s ease;box-shadow:0 4px 15px rgba(37,99,235,0.3);}"
        ".api-links a:hover{transform:translateY(-2px);box-shadow:0 6px 20px rgba(37,99,235,0.4);}"
        ".status-indicator{display:inline-block;width:8px;height:8px;border-radius:50%;background:#10b981;margin-left:8px;animation:pulse 2s infinite;}"
        "@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"
        "@media(max-width:768px){.container{padding:20px;margin:10px;}.status{grid-template-columns:1fr;gap:15px;}.value-primary{font-size:1.5rem;}}"
        "</style>"
        "<script>"
        "let tempUnit='C',sensorData={};"
        "function toggleTemp(){tempUnit=tempUnit==='C'?'F':'C';updateTempDisplay();document.querySelectorAll('.temp-btn').forEach(b=>b.classList.toggle('active'));}"
        "function updateTempDisplay(){if(sensorData.temp!==undefined){const temp=tempUnit==='F'?(sensorData.temp*9/5)+32:sensorData.temp;document.getElementById('temp-primary').textContent=temp.toFixed(1)+'°'+tempUnit;document.getElementById('temp-secondary').textContent=tempUnit==='C'?((sensorData.temp*9/5)+32).toFixed(1)+'°F':sensorData.temp.toFixed(1)+'°C';}}"
        "function getAQIStatus(value){if(value<=20)return{text:'Excellent',color:'#10b981'};if(value<=40)return{text:'Good',color:'#3b82f6'};if(value<=60)return{text:'Moderate',color:'#f59e0b'};if(value<=80)return{text:'Poor',color:'#ef4444'};return{text:'Hazardous',color:'#dc2626'};}"
        "function updateData(){"
        "fetch('/api/sensors').then(r=>r.json()).then(data=>{"
        "if(data.sensors){"
        "data.sensors.forEach(sensor=>{"
        "if(sensor.id==='DHT22'&&sensor.readings){"
        "if(sensor.readings.temperature){sensorData.temp=sensor.readings.temperature.value;updateTempDisplay();}"
        "if(sensor.readings.humidity){document.getElementById('humidity-primary').textContent=sensor.readings.humidity.value.toFixed(1)+'%';document.getElementById('humidity-secondary').textContent='RH';}"
        "}"
        "if(sensor.id==='MQ135'&&sensor.readings){"
        "if(sensor.readings.air_quality){const aqi=sensor.readings.air_quality.value;const status=getAQIStatus(aqi);document.getElementById('aqi-primary').textContent=aqi.toFixed(1)+'%';document.getElementById('aqi-secondary').textContent=status.text;document.getElementById('aqi-secondary').style.color=status.color;}"
        "}"
        "});"
        "}"
        "}).catch(e=>console.error('Error:',e));"
        "}"
        "setInterval(updateData,5000);"
        "updateData();"
        "</script>"
        "</head><body>"
        "<div class='container'>"
        "<h1>🌡️ ESP32 Environmental Monitor<span class='status-indicator'></span></h1>"
        "<div class='status'>"
        "<div class='card'>"
        "<h3><span class='card-icon'>🌡️</span>Temperature</h3>"
        "<div class='value-group'>"
        "<div class='value-primary' id='temp-primary'>--°C</div>"
        "<div class='value-secondary' id='temp-secondary'>--°F</div>"
        "</div>"
        "<div class='unit-toggle'>"
        "<button class='unit-btn temp-btn active' onclick='toggleTemp()'>°C</button>"
        "<button class='unit-btn temp-btn' onclick='toggleTemp()'>°F</button>"
        "</div>"
        "</div>"
        "<div class='card'>"
        "<h3><span class='card-icon'>💧</span>Humidity</h3>"
        "<div class='value-group'>"
        "<div class='value-primary' id='humidity-primary'>--%</div>"
        "<div class='value-secondary' id='humidity-secondary'>Relative Humidity</div>"
        "</div>"
        "</div>"
        "<div class='card'>"
        "<h3><span class='card-icon'>🌬️</span>Air Quality</h3>"
        "<div class='value-group'>"
        "<div class='value-primary' id='aqi-primary'>--%</div>"
        "<div class='value-secondary' id='aqi-secondary'>Calculating...</div>"
        "</div>"
        "</div>"
        "</div>"
        "<div class='api-links'>"
        "<h3>API Endpoints</h3>"
        "<a href='/api/status'>📊 System Status</a>"
        "<a href='/api/sensors'>🔬 Sensor Data</a>"
        "</div>"
        "</div>"
        "</body></html>";

    httpd_resp_send(req, resp_str, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static esp_err_t api_status_handler(httpd_req_t *req)
{
    char json_response[512];
    snprintf(json_response, sizeof(json_response),
        "{"
        "\"uptime\":%lu,"
        "\"freeHeap\":%lu,"
        "\"sensors\":{"
            "\"online\":%d,"
            "\"total\":2"
        "},"
        "\"wifi\":{"
            "\"connected\":true,"
            "\"rssi\":-50"
        "}"
        "}",
        current_sensor_data.uptime,
        (unsigned long)esp_get_free_heap_size(),
        current_sensor_data.sensors_online ? 2 : 0
    );

    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, json_response, strlen(json_response));
    return ESP_OK;
}

static esp_err_t api_sensors_handler(httpd_req_t *req)
{
    char json_response[1024];
    snprintf(json_response, sizeof(json_response),
        "{"
        "\"sensors\":["
            "{"
                "\"id\":\"DHT22\","
                "\"name\":\"DHT22\","
                "\"type\":\"Temperature/Humidity\","
                "\"pin\":%d,"
                "\"enabled\":true,"
                "\"online\":%s,"
                "\"readings\":{"
                    "\"temperature\":{"
                        "\"value\":%.2f,"
                        "\"unit\":\"°C\""
                    "},"
                    "\"humidity\":{"
                        "\"value\":%.2f,"
                        "\"unit\":\"%%\""
                    "}"
                "}"
            "},"
            "{"
                "\"id\":\"MQ135\","
                "\"name\":\"MQ135\","
                "\"type\":\"Air Quality\","
                "\"pin\":%d,"
                "\"enabled\":true,"
                "\"online\":%s,"
                "\"readings\":{"
                    "\"air_quality\":{"
                        "\"value\":%.2f,"
                        "\"unit\":\"%%\""
                    "},"
                    "\"raw_value\":{"
                        "\"value\":%d,"
                        "\"unit\":\"\""
                    "}"
                "}"
            "}"
        "]"
        "}",
        DHT22_PIN,
        current_sensor_data.sensors_online ? "true" : "false",
        current_sensor_data.temperature,
        current_sensor_data.humidity,
        MQ135_PIN,
        current_sensor_data.sensors_online ? "true" : "false",
        current_sensor_data.air_quality_percent,
        current_sensor_data.air_quality_raw
    );

    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, json_response, strlen(json_response));
    return ESP_OK;
}
