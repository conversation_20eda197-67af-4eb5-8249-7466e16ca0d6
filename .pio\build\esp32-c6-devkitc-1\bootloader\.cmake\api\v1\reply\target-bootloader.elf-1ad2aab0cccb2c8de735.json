{"artifacts": [{"path": "bootloader.elf"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "project", "target_link_options", "set_property", "idf_build_executable", "target_link_libraries", "add_dependencies"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/project.cmake", "CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/tools/cmake/build.cmake", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_bootloader_format/CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/CMakeLists.txt", "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/CMakeLists.txt", "main/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 72, "parent": 0}, {"command": 0, "file": 0, "line": 799, "parent": 1}, {"command": 2, "file": 0, "line": 865, "parent": 1}, {"command": 2, "file": 0, "line": 867, "parent": 1}, {"command": 2, "file": 0, "line": 869, "parent": 1}, {"command": 2, "file": 0, "line": 877, "parent": 1}, {"command": 2, "file": 0, "line": 881, "parent": 1}, {"command": 4, "file": 0, "line": 972, "parent": 1}, {"command": 3, "file": 2, "line": 738, "parent": 8}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"file": 3}, {"command": 5, "file": 3, "line": 365, "parent": 24}, {"file": 4}, {"command": 5, "file": 4, "line": 13, "parent": 26}, {"file": 5}, {"command": 5, "file": 5, "line": 215, "parent": 28}, {"file": 6}, {"command": 5, "file": 6, "line": 113, "parent": 30}, {"command": 5, "file": 6, "line": 115, "parent": 30}, {"file": 7}, {"command": 5, "file": 7, "line": 202, "parent": 33}, {"file": 8}, {"command": 5, "file": 8, "line": 7, "parent": 35}, {"command": 6, "file": 0, "line": 800, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}, {"command": 5, "file": 0, "line": 855, "parent": 1}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always"}], "defines": [{"backtrace": 10, "define": "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE"}, {"backtrace": 10, "define": "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ"}], "includes": [{"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/platform_port/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/esp32c6/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/hal/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/config"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/log/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6/include/esp32c6"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32c6"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc/esp32c6"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/dma/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/ldo/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/debug_probe/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/power_supply/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/."}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32c6/private_include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/platform_include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/riscv/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/include"}, {"backtrace": 10, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32c6/register"}, {"backtrace": 14, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader/subproject/components/micro-ecc"}, {"backtrace": 14, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader/subproject/components/micro-ecc/micro-ecc"}, {"backtrace": 15, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash/include"}, {"backtrace": 16, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_bootloader_format/include"}, {"backtrace": 38, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_app_format/include"}, {"backtrace": 17, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/include"}, {"backtrace": 17, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/bootloader_flash/include"}, {"backtrace": 17, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/private_include"}, {"backtrace": 18, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/include"}, {"backtrace": 18, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/esp32c6/include"}, {"backtrace": 39, "path": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/esp_security/include"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 37, "id": "_project_elf_src::@6890427a1f51a3e7e1df"}, {"backtrace": 10, "id": "__idf_hal::@41c1a00f56cd1e9ede18"}, {"backtrace": 11, "id": "__idf_main::@7368607ed66887415643"}], "id": "bootloader.elf::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-march=rv32imac_zicsr_zifencei", "role": "flags"}, {"fragment": "-nostartfiles -march=rv32imac_zicsr_zifencei", "role": "flags"}, {"backtrace": 3, "fragment": "-Wl,--cref", "role": "flags"}, {"backtrace": 4, "fragment": "-Wl,--defsym=IDF_TARGET_ESP32C6=0", "role": "flags"}, {"backtrace": 5, "fragment": "-Wl,--Map=C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/bootloader.map", "role": "flags"}, {"backtrace": 6, "fragment": "-Wl,--no-warn-rwx-segments", "role": "flags"}, {"backtrace": 7, "fragment": "-Wl,--orphan-handling=warn", "role": "flags"}, {"backtrace": 9, "fragment": "-fno-rtti", "role": "flags"}, {"backtrace": 9, "fragment": "-fno-lto", "role": "flags"}, {"backtrace": 9, "fragment": "-Wl,--gc-sections", "role": "flags"}, {"backtrace": 9, "fragment": "-Wl,--warn-common", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.api.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.rvfp.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.wdt.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.systimer.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.version.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.libc.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.libc-suboptimal_for_misaligned_mem.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.rom.newlib.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "rom.api.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-T", "role": "flags"}, {"backtrace": 10, "fragment": "esp32c6.peripherals.ld", "role": "flags"}, {"backtrace": 11, "fragment": "-T", "role": "flags"}, {"backtrace": 11, "fragment": "bootloader.ld", "role": "flags"}, {"backtrace": 11, "fragment": "-T", "role": "flags"}, {"backtrace": 11, "fragment": "bootloader.rom.ld", "role": "flags"}, {"backtrace": 10, "fragment": "-LC:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_rom\\esp32c6\\ld", "role": "libraryPath"}, {"backtrace": 10, "fragment": "-LC:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\riscv\\ld", "role": "libraryPath"}, {"backtrace": 10, "fragment": "-LC:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\soc\\esp32c6\\ld", "role": "libraryPath"}, {"backtrace": 11, "fragment": "-LC:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\bootloader\\subproject\\main\\ld\\esp32c6", "role": "libraryPath"}, {"backtrace": 10, "fragment": "esp-idf\\hal\\libhal.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf\\riscv\\libriscv.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf\\soc\\libsoc.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf\\micro-ecc\\libmicro-ecc.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf\\spi_flash\\libspi_flash.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf\\esp_bootloader_format\\libesp_bootloader_format.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf\\bootloader_support\\libbootloader_support.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf\\efuse\\libefuse.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf\\esp_system\\libesp_system.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf\\esp_hw_support\\libesp_hw_support.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf\\esp_common\\libesp_common.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf\\esp_rom\\libesp_rom.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf\\log\\liblog.a", "role": "libraries"}, {"backtrace": 11, "fragment": "esp-idf\\main\\libmain.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf\\hal\\libhal.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf\\riscv\\libriscv.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf\\soc\\libsoc.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf\\micro-ecc\\libmicro-ecc.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf\\spi_flash\\libspi_flash.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf\\esp_bootloader_format\\libesp_bootloader_format.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf\\bootloader_support\\libbootloader_support.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf\\efuse\\libefuse.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf\\esp_system\\libesp_system.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf\\esp_hw_support\\libesp_hw_support.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf\\esp_common\\libesp_common.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf\\esp_rom\\libesp_rom.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf\\log\\liblog.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf\\hal\\libhal.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf\\riscv\\libriscv.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf\\soc\\libsoc.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf\\micro-ecc\\libmicro-ecc.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf\\spi_flash\\libspi_flash.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf\\esp_bootloader_format\\libesp_bootloader_format.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf\\bootloader_support\\libbootloader_support.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf\\efuse\\libefuse.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf\\esp_system\\libesp_system.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf\\esp_hw_support\\libesp_hw_support.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf\\esp_common\\libesp_common.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf\\esp_rom\\libesp_rom.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf\\log\\liblog.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf\\hal\\libhal.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf\\riscv\\libriscv.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf\\soc\\libsoc.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf\\micro-ecc\\libmicro-ecc.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf\\spi_flash\\libspi_flash.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf\\esp_bootloader_format\\libesp_bootloader_format.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf\\bootloader_support\\libbootloader_support.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf\\efuse\\libefuse.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf\\esp_system\\libesp_system.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf\\esp_hw_support\\libesp_hw_support.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf\\esp_common\\libesp_common.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf\\esp_rom\\libesp_rom.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf\\log\\liblog.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf\\hal\\libhal.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf\\riscv\\libriscv.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf\\soc\\libsoc.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf\\micro-ecc\\libmicro-ecc.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf\\spi_flash\\libspi_flash.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf\\esp_bootloader_format\\libesp_bootloader_format.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf\\bootloader_support\\libbootloader_support.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf\\efuse\\libefuse.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf\\esp_system\\libesp_system.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf\\esp_hw_support\\libesp_hw_support.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf\\esp_common\\libesp_common.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf\\esp_rom\\libesp_rom.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf\\log\\liblog.a", "role": "libraries"}, {"backtrace": 25, "fragment": "-u __assert_func", "role": "libraries"}, {"backtrace": 27, "fragment": "-u esp_bootloader_desc", "role": "libraries"}, {"backtrace": 29, "fragment": "-u abort", "role": "libraries"}, {"backtrace": 31, "fragment": "-u __ubsan_include", "role": "libraries"}, {"backtrace": 32, "fragment": "-u esp_system_include_startup_funcs", "role": "libraries"}, {"backtrace": 34, "fragment": "-u esp_sleep_gpio_include", "role": "libraries"}, {"backtrace": 36, "fragment": "-u bootloader_hooks_include", "role": "libraries"}], "language": "C"}, "name": "bootloader.elf", "nameOnDisk": "bootloader.elf", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/project_elf_src_esp32c6.c", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/project_elf_src_esp32c6.c.rule", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}