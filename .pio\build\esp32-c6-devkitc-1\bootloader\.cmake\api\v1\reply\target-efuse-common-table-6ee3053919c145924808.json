{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 66, "parent": 0}]}, "id": "efuse-common-table::@2da764c922f8b73e9c75", "name": "efuse-common-table", "paths": {"build": "esp-idf/efuse", "source": "C:/Users/<USER>/.platformio/packages/framework-espidf/components/efuse"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/esp-idf/efuse/CMakeFiles/efuse-common-table", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/esp-idf/efuse/CMakeFiles/efuse-common-table.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}