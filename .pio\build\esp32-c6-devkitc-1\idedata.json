{"build_type": "release", "env_name": "esp32-c6-devkitc-1", "libsource_dirs": ["C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\lib", "C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\.pio\\libdeps\\esp32-c6-devkitc-1", "C:\\Users\\<USER>\\.platformio\\lib"], "defines": ["_POSIX_READER_WRITER_LOCKS", "_GNU_SOURCE", "_GLIBCXX_USE_POSIX_SEMAPHORE", "_GLIBCXX_HAVE_POSIX_SEMAPHORE", "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ", "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE", "IDF_VER=\"5.5.0\"", "ESP_PLATFORM", "PLATFORMIO=60118", "CORE_DEBUG_LEVEL=3", "CONFIG_ARDUHAL_LOG_COLORS=1"], "includes": {"build": ["C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\include", "C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\riscv\\include", "C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\.pio\\build\\esp32-c6-devkitc-1\\config", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\newlib\\platform_include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\freertos\\config\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\freertos\\config\\include\\freertos", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\freertos\\config\\riscv\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\freertos\\FreeRTOS-Kernel\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\freertos\\FreeRTOS-Kernel\\portable\\riscv\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\freertos\\FreeRTOS-Kernel\\portable\\riscv\\include\\freertos", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\freertos\\esp_additions\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\include\\soc", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\include\\soc\\esp32c6", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\dma\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\ldo\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\debug_probe\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\mspi_timing_tuning\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\mspi_timing_tuning\\tuning_scheme_impl\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\power_supply\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\port\\esp32c6", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\port\\esp32c6\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hw_support\\port\\esp32c6\\private_include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\heap\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\heap\\tlsf", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\log\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\soc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\soc\\esp32c6", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\soc\\esp32c6\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\soc\\esp32c6\\register", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\hal\\platform_port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\hal\\esp32c6\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\hal\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_rom\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_rom\\esp32c6\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_rom\\esp32c6\\include\\esp32c6", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_rom\\esp32c6", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_system\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_system\\port\\soc", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_system\\port\\include\\riscv", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_system\\port\\include\\private", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\include\\apps", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\include\\apps\\sntp", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\lwip\\src\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\port\\freertos\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\port\\esp32xx\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\port\\esp32xx\\include\\arch", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\lwip\\port\\esp32xx\\include\\sys", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_gpio\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_timer\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_pm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mbedtls\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mbedtls\\mbedtls\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mbedtls\\mbedtls\\library", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mbedtls\\esp_crt_bundle\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mbedtls\\mbedtls\\3rdparty\\everest\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mbedtls\\mbedtls\\3rdparty\\p256-m", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mbedtls\\mbedtls\\3rdparty\\p256-m\\p256-m", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_app_format\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_bootloader_format\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\app_update\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\bootloader_support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\bootloader_support\\bootloader_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_partition\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\efuse\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\efuse\\esp32c6\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_mm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\spi_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_security\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\pthread\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_gptimer\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_ringbuf\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_uart\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\vfs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\app_trace\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_event\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\nvs_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_phy\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_phy\\esp32c6\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_usb_serial_jtag\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_vfs_console\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_netif\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\wpa_supplicant\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\wpa_supplicant\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\wpa_supplicant\\esp_supplicant\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_coex\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_wifi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_wifi\\include\\local", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_wifi\\wifi_apps\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_wifi\\wifi_apps\\nan_app\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_spi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_gdbstub\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\unity\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\unity\\unity\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\cmock\\CMock\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\console", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_pcnt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_mcpwm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_ana_cmpr\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_i2s\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\sdmmc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_sdmmc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_sdspi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_sdio\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_dac\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_bitscrambler\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_rmt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_tsens\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_sdm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_i2c\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_ledc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_parlio\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_twai\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\driver\\deprecated", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\driver\\i2c\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\driver\\touch_sensor\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\driver\\twai\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\http_parser", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp-tls", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp-tls\\esp-tls-crypto", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_adc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_adc\\interface", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_adc\\esp32c6\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_adc\\deprecated\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_isp\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_cam\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_cam\\interface", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_psram\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_jpeg\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_driver_ppa\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_eth\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_hid\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\tcp_transport\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_http_client\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_http_server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_https_ota\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_https_server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_lcd\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_lcd\\interface", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\protobuf-c\\protobuf-c", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\protocomm\\include\\common", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\protocomm\\include\\security", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\protocomm\\include\\transports", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\protocomm\\include\\crypto\\srp6a", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\protocomm\\proto-c", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_local_ctrl\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\esp_tee\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\espcoredump\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\espcoredump\\include\\port\\riscv", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\wear_levelling\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\fatfs\\diskio", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\fatfs\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\fatfs\\vfs", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\idf_test\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\idf_test\\include\\esp32c6", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\ieee802154\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\json\\cJSON", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\mqtt\\esp-mqtt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\nvs_sec_provider\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\rt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\spiffs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-espidf\\components\\wifi_provisioning\\include", "C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\src"], "compatlib": ["C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\.pio\\libdeps\\esp32-c6-devkitc-1\\ArduinoJson\\src"], "toolchain": ["C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\riscv32-esp-elf\\include\\c++\\14.2.0", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\riscv32-esp-elf\\include\\c++\\14.2.0\\riscv32-esp-elf", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\lib\\gcc\\riscv32-esp-elf\\14.2.0\\include", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\lib\\gcc\\riscv32-esp-elf\\14.2.0\\include-fixed", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\picolibc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\riscv32-esp-elf\\include"]}, "cc_flags": ["-Og", "-Wall", "-Werror=all", "-Wextra", "-Wno-enum-conversion", "-Wno-error=deprecated-declarations", "-Wno-error=extra", "-Wno-error=unused-but-set-variable", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-old-style-declaration", "-Wno-sign-compare", "-Wno-unused-parameter", "-fdata-sections", "-fdiagnostics-color=always", "-ffunction-sections", "-fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf=/IDF", "-fmacro-prefix-map=C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver=.", "-fno-jump-tables", "-fno-shrink-wrap", "-fno-tree-switch-conversion", "-fstrict-volatile-bitfields", "-gdwarf-4", "-ggdb", "-march=rv32imac_zicsr_zifencei", "-nostartfiles", "-std=gnu17"], "cxx_flags": ["-Og", "-Wall", "-Werror=all", "-Wextra", "-Wno-enum-conversion", "-Wno-error=deprecated-declarations", "-Wno-error=extra", "-Wno-error=unused-but-set-variable", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-sign-compare", "-Wno-unused-parameter", "-fdata-sections", "-fdiagnostics-color=always", "-ffunction-sections", "-fmacro-prefix-map=C:/Users/<USER>/.platformio/packages/framework-espidf=/IDF", "-fmacro-prefix-map=C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver=.", "-fno-exceptions", "-fno-jump-tables", "-fno-rtti", "-fno-shrink-wrap", "-fno-tree-switch-conversion", "-fstrict-volatile-bitfields", "-fuse-cxa-atexit", "-gdwarf-4", "-ggdb", "-march=rv32imac_zicsr_zifencei", "-nostartfiles", "-std=gnu++2b"], "cc_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-gcc.exe", "cxx_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp\\bin\\riscv32-esp-elf-g++.exe", "gdb_path": "C:\\Users\\<USER>\\.platformio\\packages\\tool-riscv32-esp-elf-gdb\\bin\\riscv32-esp-elf-gdb.exe", "prog_path": "C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\.pio\\build\\esp32-c6-devkitc-1\\firmware.elf", "svd_path": null, "compiler_type": "gcc", "targets": [{"name": "menuconfig", "title": "<PERSON>", "description": null, "group": "Platform"}, {"name": "buildfs", "title": "Build Filesystem Image", "description": null, "group": "Platform"}, {"name": "size", "title": "Program Size", "description": "Calculate program size", "group": "Platform"}, {"name": "upload", "title": "Upload", "description": null, "group": "Platform"}, {"name": "uploadfs", "title": "Upload Filesystem Image", "description": null, "group": "Platform"}, {"name": "uploadfsota", "title": "Upload Filesystem Image OTA", "description": null, "group": "Platform"}, {"name": "encrypt", "title": "Encrypt Application Images", "description": null, "group": "Platform"}, {"name": "sign", "title": "Sign Application Images", "description": null, "group": "Platform"}, {"name": "app", "title": "Build Application", "description": null, "group": "Platform"}, {"name": "bootloader", "title": "Build Bootloader", "description": null, "group": "Platform"}, {"name": "__upload-signed", "title": "Upload Signed ", "description": null, "group": "Platform"}, {"name": "__upload-encrypted", "title": "Upload Encrypted ", "description": null, "group": "Platform"}, {"name": "__upload-signed-encrypted", "title": "Upload Signed Encrypted ", "description": null, "group": "Platform"}, {"name": "__upload-app", "title": "Upload App", "description": null, "group": "Platform"}, {"name": "__upload-signed-app", "title": "Upload Signed App", "description": null, "group": "Platform"}, {"name": "__upload-encrypted-app", "title": "Upload Encrypted App", "description": null, "group": "Platform"}, {"name": "__upload-signed-encrypted-app", "title": "Upload Signed Encrypted App", "description": null, "group": "Platform"}, {"name": "__upload-bootloader", "title": "Upload Bootloader", "description": null, "group": "Platform"}, {"name": "__upload-signed-bootloader", "title": "Upload Signed Bootloader", "description": null, "group": "Platform"}, {"name": "__upload-encrypted-bootloader", "title": "Upload Encrypted Bootloader", "description": null, "group": "Platform"}, {"name": "__upload-signed-encrypted-bootloader", "title": "Upload Signed Encrypted Bootloader", "description": null, "group": "Platform"}, {"name": "erase", "title": "Erase Flash", "description": null, "group": "Platform"}], "extra": {"flash_images": [{"offset": 0, "path": "C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\.pio\\build\\esp32-c6-devkitc-1\\bootloader.bin"}, {"offset": "0x8000", "path": "C:\\Users\\<USER>\\Desktop\\Projects\\ESP32\\ESP32-C6-DevKitC-1_v1.2\\temp_monitor_w_webserver\\.pio\\build\\esp32-c6-devkitc-1\\partitions.bin"}], "application_offset": "0x10000"}}