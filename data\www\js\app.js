// Main Application JavaScript
class EnvironmentalMonitor {
    constructor() {
        this.currentPage = 'dashboard';
        this.updateInterval = null;
        this.connectionStatus = true;
        this.sensorData = {};
        this.systemStatus = {};
        
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupEventListeners();
        this.startDataUpdates();
        this.updateDateTime();
        
        // Update time every second
        setInterval(() => this.updateDateTime(), 1000);
    }

    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const pages = document.querySelectorAll('.page');

        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const targetPage = item.dataset.page;
                this.switchPage(targetPage);
                
                // Update active nav item
                navItems.forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
            });
        });
    }

    switchPage(pageName) {
        const pages = document.querySelectorAll('.page');
        const pageTitle = document.getElementById('page-title');
        
        // Hide all pages
        pages.forEach(page => page.classList.remove('active'));
        
        // Show target page
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageName;
            
            // Update page title
            const titles = {
                'dashboard': 'Dashboard',
                'sensors': 'Sensor Management',
                'air-quality': 'Air Quality',
                'environment': 'Environmental Data',
                'settings': 'Settings'
            };
            pageTitle.textContent = titles[pageName] || 'Dashboard';
        }
    }

    setupEventListeners() {
        // Chart timeframe selector
        const chartTimeframe = document.getElementById('chart-timeframe');
        if (chartTimeframe) {
            chartTimeframe.addEventListener('change', (e) => {
                this.updateCharts(e.target.value);
            });
        }
    }

    startDataUpdates() {
        // Initial data load
        this.updateSystemStatus();
        this.updateSensorData();
        
        // Set up periodic updates
        this.updateInterval = setInterval(() => {
            this.updateSystemStatus();
            this.updateSensorData();
        }, 30000); // Update every 30 seconds
    }

    async updateSystemStatus() {
        try {
            const response = await fetch('/api/status');
            if (response.ok) {
                this.systemStatus = await response.json();
                this.renderSystemStatus();
                this.updateConnectionStatus(true);
            } else {
                throw new Error('Failed to fetch system status');
            }
        } catch (error) {
            console.error('Error updating system status:', error);
            this.updateConnectionStatus(false);
        }
    }

    async updateSensorData() {
        try {
            const response = await fetch('/api/sensors');
            if (response.ok) {
                this.sensorData = await response.json();
                this.renderSensorData();
                this.updateConnectionStatus(true);
            } else {
                throw new Error('Failed to fetch sensor data');
            }
        } catch (error) {
            console.error('Error updating sensor data:', error);
            this.updateConnectionStatus(false);
        }
    }

    renderSystemStatus() {
        const status = this.systemStatus;
        
        // Update uptime
        const uptimeElement = document.getElementById('uptime');
        if (uptimeElement && status.uptime) {
            const uptime = this.formatUptime(status.uptime);
            uptimeElement.textContent = uptime;
        }

        // Update sensors online count
        const sensorsOnlineElement = document.getElementById('sensors-online');
        if (sensorsOnlineElement && status.sensors) {
            sensorsOnlineElement.textContent = `${status.sensors.online}/${status.sensors.total}`;
        }

        // Update WiFi signal
        const wifiSignalElement = document.getElementById('wifi-signal');
        if (wifiSignalElement && status.wifi) {
            const signalStrength = this.getSignalStrength(status.wifi.rssi);
            wifiSignalElement.textContent = signalStrength;
        }

        // Update logging status
        const loggingStatusElement = document.getElementById('logging-status');
        if (loggingStatusElement && status.dataLogging) {
            loggingStatusElement.textContent = status.dataLogging.enabled ? 'Enabled' : 'Disabled';
            loggingStatusElement.className = status.dataLogging.enabled ? 'value status-enabled' : 'value';
        }
    }

    renderSensorData() {
        if (!this.sensorData.sensors) return;

        this.sensorData.sensors.forEach(sensor => {
            if (sensor.id === 'DHT22' && sensor.readings) {
                // Update temperature
                if (sensor.readings.temperature) {
                    const tempElement = document.getElementById('temperature-value');
                    if (tempElement) {
                        tempElement.textContent = sensor.readings.temperature.value.toFixed(1);
                    }
                    this.updateTemperatureGauge(sensor.readings.temperature.value);
                }

                // Update humidity
                if (sensor.readings.humidity) {
                    const humidityElement = document.getElementById('humidity-value');
                    if (humidityElement) {
                        humidityElement.textContent = sensor.readings.humidity.value.toFixed(1);
                    }
                    this.updateHumidityGauge(sensor.readings.humidity.value);
                }
            }

            if (sensor.id === 'MQ135' && sensor.readings) {
                // Update air quality
                if (sensor.readings.air_quality) {
                    const aqiElement = document.getElementById('aqi-value');
                    const aqiStatusElement = document.getElementById('aqi-status');
                    const aqiCircle = document.getElementById('aqi-circle');
                    
                    if (aqiElement && aqiStatusElement && aqiCircle) {
                        const airQuality = sensor.readings.air_quality.value;
                        aqiElement.textContent = Math.round(airQuality);
                        
                        const status = this.getAirQualityStatus(airQuality);
                        aqiStatusElement.textContent = status.text;
                        aqiCircle.style.background = status.gradient;
                    }
                }
            }
        });
    }

    updateConnectionStatus(isOnline) {
        this.connectionStatus = isOnline;
        const statusIndicator = document.querySelector('.status-indicator');
        const statusDot = document.getElementById('connection-status');
        const statusText = statusIndicator.querySelector('span');

        if (isOnline) {
            statusIndicator.style.background = 'var(--success-color)';
            statusDot.classList.add('online');
            statusText.textContent = 'Online';
        } else {
            statusIndicator.style.background = 'var(--danger-color)';
            statusDot.classList.remove('online');
            statusText.textContent = 'Offline';
        }
    }

    updateDateTime() {
        const now = new Date();
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = now.toLocaleString();
        }
    }

    formatUptime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}d ${hours % 24}h ${minutes % 60}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else {
            return `${minutes}m ${seconds % 60}s`;
        }
    }

    getSignalStrength(rssi) {
        if (rssi >= -50) return 'Excellent';
        if (rssi >= -60) return 'Good';
        if (rssi >= -70) return 'Fair';
        return 'Poor';
    }

    getAirQualityStatus(value) {
        if (value <= 20) {
            return {
                text: 'Excellent',
                gradient: 'linear-gradient(135deg, #10b981 0%, #06b6d4 100%)'
            };
        } else if (value <= 40) {
            return {
                text: 'Good',
                gradient: 'linear-gradient(135deg, #22c55e 0%, #10b981 100%)'
            };
        } else if (value <= 60) {
            return {
                text: 'Moderate',
                gradient: 'linear-gradient(135deg, #f59e0b 0%, #eab308 100%)'
            };
        } else if (value <= 80) {
            return {
                text: 'Poor',
                gradient: 'linear-gradient(135deg, #f97316 0%, #f59e0b 100%)'
            };
        } else {
            return {
                text: 'Hazardous',
                gradient: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
            };
        }
    }

    updateTemperatureGauge(temperature) {
        // Temperature gauge will be implemented with Chart.js
        if (window.temperatureChart) {
            window.temperatureChart.updateGauge(temperature);
        }
    }

    updateHumidityGauge(humidity) {
        // Humidity gauge will be implemented with Chart.js
        if (window.humidityChart) {
            window.humidityChart.updateGauge(humidity);
        }
    }

    updateCharts(timeframe) {
        // Update charts based on selected timeframe
        if (window.recentDataChart) {
            window.recentDataChart.updateTimeframe(timeframe);
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new EnvironmentalMonitor();
});
