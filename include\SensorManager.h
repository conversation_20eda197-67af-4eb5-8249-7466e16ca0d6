#ifndef SENSOR_MANAGER_H
#define SENSOR_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <map>

struct SensorReading {
  String name;
  float value;
  String unit;
  unsigned long timestamp;
};

struct Sensor {
  String id;
  String name;
  String type;
  int pin;
  bool enabled;
  bool online;
  std::map<String, SensorReading> readings;
  unsigned long lastUpdate;
};

class SensorManager {
private:
  std::vector<Sensor> sensors;
  
public:
  SensorManager();
  void begin();
  
  // Sensor management
  bool addSensor(String id, String type, int pin, bool enabled = true);
  bool removeSensor(String id);
  bool updateSensorConfig(String id, String name, String type, int pin, bool enabled);
  
  // Sensor data
  void updateSensorValue(String sensorId, String readingName, float value, String unit = "");
  SensorReading getSensorReading(String sensorId, String readingName);
  std::vector<Sensor> getAllSensors();
  JsonDocument getAllSensorData();
  
  // Status
  int getOnlineSensorCount();
  int getTotalSensorCount();
  bool isSensorOnline(String sensorId);
  
  // Configuration
  JsonDocument getSensorConfig();
  bool loadSensorConfig(JsonDocument config);
  void saveConfigToFile();
  void loadConfigFromFile();
};

#endif
