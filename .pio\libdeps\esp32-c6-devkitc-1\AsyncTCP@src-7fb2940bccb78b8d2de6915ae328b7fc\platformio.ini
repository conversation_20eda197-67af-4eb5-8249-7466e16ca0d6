[platformio]
default_envs = arduino-2, arduino-3, arduino-311
lib_dir = .
src_dir = examples/Client

[env]
framework = arduino
build_flags = 
  -Wall -Wextra
  -D CONFIG_ASYNC_TCP_MAX_ACK_TIME=5000
  -D CONFIG_ASYNC_TCP_PRIORITY=10
  -D CONFIG_ASYNC_TCP_QUEUE_SIZE=64
  -D CONFIG_ASYNC_TCP_RUNNING_CORE=1
  -D CONFIG_ASYNC_TCP_STACK_SIZE=4096
  -D CONFIG_ARDUHAL_LOG_COLORS
  -D CORE_DEBUG_LEVEL=ARDUHAL_LOG_LEVEL_DEBUG
upload_protocol = esptool
monitor_speed = 115200
monitor_filters = esp32_exception_decoder, log2file
board = esp32dev

[env:arduino-2]
platform = espressif32@6.9.0

[env:arduino-3]
platform = https://github.com/pioarduino/platform-espressif32/releases/download/51.03.05/platform-espressif32.zip

[env:arduino-311]
platform = https://github.com/pioarduino/platform-espressif32/releases/download/53.03.11/platform-espressif32.zip

;  CI

[env:ci-arduino-2]
platform = espressif32@6.9.0
board = ${sysenv.PIO_BOARD}

[env:ci-arduino-3]
platform = https://github.com/pioarduino/platform-espressif32/releases/download/51.03.05/platform-espressif32.zip
board = ${sysenv.PIO_BOARD}

[env:ci-arduino-311]
platform = https://github.com/pioarduino/platform-espressif32/releases/download/53.03.11/platform-espressif32.zip
board = ${sysenv.PIO_BOARD}
