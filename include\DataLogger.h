#ifndef DATA_LOGGER_H
#define DATA_LOGGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>

struct DataPoint {
  unsigned long timestamp;
  String sensorId;
  String readingName;
  float value;
  String unit;
};

class DataLogger {
private:
  String currentLogFile;
  bool loggingEnabled;
  unsigned long maxLogSize;
  int maxLogFiles;
  
  String generateLogFileName();
  void rotateLogFiles();
  bool writeDataPoint(DataPoint point);
  
public:
  DataLogger();
  void begin();
  
  // Logging control
  void enableLogging(bool enable = true);
  bool isLoggingEnabled();
  
  // Data logging
  bool logSensorReading(String sensorId, String readingName, float value, String unit = "");
  bool logCurrentData(JsonDocument sensorData);
  
  // Data retrieval
  JsonDocument getHistoricalData(String sensorId, String readingName, unsigned long startTime, unsigned long endTime);
  JsonDocument getRecentData(String sensorId, String readingName, int hours = 24);
  
  // File management
  void clearLogs();
  JsonDocument getLogFileInfo();
  unsigned long getTotalLogSize();
};

#endif
