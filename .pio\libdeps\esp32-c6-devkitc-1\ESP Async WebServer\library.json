{"name": "ESP Async WebServer", "description": "Asynchronous HTTP and WebSocket Server Library for ESP8266 and ESP32", "keywords": "http,async,websocket,webserver", "authors": {"name": "<PERSON><PERSON>", "maintainer": true}, "repository": {"type": "git", "url": "https://github.com/me-no-dev/ESPAsyncWebServer.git"}, "version": "1.2.4", "license": "LGPL-3.0", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["espressif8266", "espressif32"], "dependencies": [{"owner": "me-no-dev", "name": "ESPAsyncTCP", "version": "^1.2.2", "platforms": "espressif8266"}, {"owner": "me-no-dev", "name": "AsyncTCP", "version": "^1.1.1", "platforms": "espressif32"}, {"name": "Hash", "platforms": "espressif8266"}]}