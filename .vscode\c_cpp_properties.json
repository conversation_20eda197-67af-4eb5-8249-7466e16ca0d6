{"configurations": [{"name": "PlatformIO", "includePath": ["${workspaceFolder}/include", "${workspaceFolder}/src", "${workspaceFolder}/.pio/libdeps/esp32-c6-devkitc-1/**", "${workspaceFolder}/.pio/build/esp32-c6-devkitc-1/config", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/**", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/freertos/esp_additions/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/esp_common/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/esp_system/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/esp_wifi/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/esp_event/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/log/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/esp_netif/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/esp_http_server/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/spiffs/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/nvs_flash/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/driver/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/esp_adc/include", "${env:USERPROFILE}/.platformio/packages/framework-espidf/components/json/cJSON", "${env:USERPROFILE}/.platformio/packages/toolchain-riscv32-esp/riscv32-esp-elf/include"], "defines": ["PLATFORMIO=60111", "ESP_PLATFORM", "IDF_VER=\"5.5.0\"", "ESP32C6", "_GNU_SOURCE", "_POSIX_READER_WRITER_LOCKS"], "compilerPath": "${env:USERPROFILE}/.platformio/packages/toolchain-riscv32-esp/bin/riscv32-esp-elf-gcc.exe", "cStandard": "gnu17", "cppStandard": "gnu++23", "intelliSenseMode": "gcc-x64", "compilerArgs": ["-mlongcalls", "-Wno-frame-address"]}], "version": 4}