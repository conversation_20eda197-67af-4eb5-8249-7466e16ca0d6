#ifndef CONFIG_H
#define CONFIG_H

// WiFi Configuration
// Update these with your network credentials
#define WIFI_SSID "Phones"
#define WIFI_PASSWORD "Caden1234"

// Pin Configuration (from pinout.md)
#define DHT22_PIN 21
#define MQ135_PIN 13

// Sensor Configuration
#define SENSOR_READ_INTERVAL 30000    // 30 seconds
#define DATA_LOG_INTERVAL 300000      // 5 minutes
#define MAX_SENSOR_OFFLINE_TIME 60000 // 1 minute

// Web Server Configuration
#define WEB_SERVER_PORT 80
#define API_TIMEOUT 10000

// Data Logging Configuration
#define MAX_LOG_FILE_SIZE 1048576     // 1MB
#define MAX_LOG_FILES 10
#define LOG_RETENTION_DAYS 30

// System Configuration
#define SERIAL_BAUD_RATE 115200
#define WATCHDOG_TIMEOUT 30000        // 30 seconds

// NTP Configuration
#define NTP_SERVER1 "pool.ntp.org"
#define NTP_SERVER2 "time.nist.gov"
#define TIMEZONE_OFFSET 0             // UTC offset in seconds

// Sensor Thresholds
#define TEMP_MIN_THRESHOLD -40.0
#define TEMP_MAX_THRESHOLD 80.0
#define HUMIDITY_MIN_THRESHOLD 0.0
#define HUMIDITY_MAX_THRESHOLD 100.0
#define AIR_QUALITY_GOOD_THRESHOLD 20.0
#define AIR_QUALITY_MODERATE_THRESHOLD 40.0
#define AIR_QUALITY_POOR_THRESHOLD 60.0
#define AIR_QUALITY_HAZARDOUS_THRESHOLD 80.0

// Available GPIO pins for ESP32-C6
// Digital pins: 0-23 (some have restrictions)
// Analog pins: 0-6 (ADC1), 7-13 (ADC2)
// Note: Some pins are reserved for flash, UART, etc.

#endif
