#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <DHT.h>
#include <SPIFFS.h>
#include <time.h>
#include "config.h"
#include "SensorManager.h"
#include "WebServerHandler.h"
#include "DataLogger.h"

// WiFi credentials from config.h
const char* ssid = WIFI_SSID;
const char* password = WIFI_PASSWORD;

// Global objects
AsyncWebServer server(80);
SensorManager sensorManager;
WebServerHandler webHandler(&server, &sensorManager);
DataLogger dataLogger;

// DHT sensor
DHT dht(DHT22_PIN, DHT22);

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("ESP32 Environmental Monitor Starting...");
  
  // Initialize SPIFFS for web files
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS Mount Failed");
    return;
  }
  
  // Initialize sensors
  dht.begin();
  sensorManager.begin();
  
  // Add initial sensors
  sensorManager.addSensor("DHT22", "Temperature/Humidity", DHT22_PIN, true);
  sensorManager.addSensor("MQ135", "Air Quality", MQ135_PIN, true);
  
  // Initialize data logger
  dataLogger.begin();
  
  // Connect to WiFi
  WiFi.begin(ssid, password);
  Serial.print("Connecting to WiFi");
  
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  
  Serial.println();
  Serial.print("Connected! IP address: ");
  Serial.println(WiFi.localIP());
  
  // Configure time
  configTime(0, 0, "pool.ntp.org", "time.nist.gov");
  
  // Initialize web server
  webHandler.begin();
  
  Serial.println("Web server started");
  Serial.println("Access the dashboard at: http://" + WiFi.localIP().toString());
}

void loop() {
  // Read sensors every 30 seconds
  static unsigned long lastSensorRead = 0;
  if (millis() - lastSensorRead > 30000) {
    readSensors();
    lastSensorRead = millis();
  }
  
  // Log data every 5 minutes
  static unsigned long lastDataLog = 0;
  if (millis() - lastDataLog > 300000) {
    dataLogger.logCurrentData(sensorManager.getAllSensorData());
    lastDataLog = millis();
  }
  
  delay(1000);
}

void readSensors() {
  // Read DHT22
  float temperature = dht.readTemperature();
  float humidity = dht.readHumidity();
  
  if (!isnan(temperature) && !isnan(humidity)) {
    sensorManager.updateSensorValue("DHT22", "temperature", temperature);
    sensorManager.updateSensorValue("DHT22", "humidity", humidity);
    Serial.printf("DHT22 - Temp: %.2f°C, Humidity: %.2f%%\n", temperature, humidity);
  }
  
  // Read MQ135 (analog value)
  int mq135Value = analogRead(MQ135_PIN);
  float airQuality = map(mq135Value, 0, 4095, 0, 100); // Convert to percentage
  
  sensorManager.updateSensorValue("MQ135", "air_quality", airQuality);
  sensorManager.updateSensorValue("MQ135", "raw_value", mq135Value);
  Serial.printf("MQ135 - Air Quality: %.2f%%, Raw: %d\n", airQuality, mq135Value);
}
