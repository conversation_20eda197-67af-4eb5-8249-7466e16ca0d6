[env:esp32-c6-devkitc-1]
platform = espressif32
board = esp32-c6-devkitc-1
framework = espidf
monitor_speed = 115200
upload_port = COM10
monitor_port = COM10

; ESP-IDF specific libraries will be handled via components

; Build flags
build_flags =
    -DCORE_DEBUG_LEVEL=3
    -DCONFIG_ARDUHAL_LOG_COLORS=1

; Custom partition table
board_build.partitions = partitions.csv

; Upload settings
upload_speed = 921600

; Monitor settings
monitor_filters = esp32_exception_decoder
