#include "SensorManager.h"
#include <SPIFFS.h>

SensorManager::SensorManager() {
}

void SensorManager::begin() {
  loadConfigFromFile();
}

bool SensorManager::addSensor(String id, String type, int pin, bool enabled) {
  // Check if sensor already exists
  for (auto& sensor : sensors) {
    if (sensor.id == id) {
      return false; // Sensor already exists
    }
  }
  
  Sensor newSensor;
  newSensor.id = id;
  newSensor.name = id;
  newSensor.type = type;
  newSensor.pin = pin;
  newSensor.enabled = enabled;
  newSensor.online = false;
  newSensor.lastUpdate = 0;
  
  sensors.push_back(newSensor);
  saveConfigToFile();
  return true;
}

bool SensorManager::removeSensor(String id) {
  for (auto it = sensors.begin(); it != sensors.end(); ++it) {
    if (it->id == id) {
      sensors.erase(it);
      saveConfigToFile();
      return true;
    }
  }
  return false;
}

bool SensorManager::updateSensorConfig(String id, String name, String type, int pin, bool enabled) {
  for (auto& sensor : sensors) {
    if (sensor.id == id) {
      sensor.name = name;
      sensor.type = type;
      sensor.pin = pin;
      sensor.enabled = enabled;
      saveConfigToFile();
      return true;
    }
  }
  return false;
}

void SensorManager::updateSensorValue(String sensorId, String readingName, float value, String unit) {
  for (auto& sensor : sensors) {
    if (sensor.id == sensorId) {
      SensorReading reading;
      reading.name = readingName;
      reading.value = value;
      reading.unit = unit;
      reading.timestamp = millis();
      
      sensor.readings[readingName] = reading;
      sensor.online = true;
      sensor.lastUpdate = millis();
      return;
    }
  }
}

SensorReading SensorManager::getSensorReading(String sensorId, String readingName) {
  for (auto& sensor : sensors) {
    if (sensor.id == sensorId) {
      auto it = sensor.readings.find(readingName);
      if (it != sensor.readings.end()) {
        return it->second;
      }
    }
  }
  return SensorReading(); // Return empty reading if not found
}

std::vector<Sensor> SensorManager::getAllSensors() {
  return sensors;
}

JsonDocument SensorManager::getAllSensorData() {
  JsonDocument doc;
  JsonArray sensorArray = doc["sensors"].to<JsonArray>();
  
  for (auto& sensor : sensors) {
    JsonObject sensorObj = sensorArray.add<JsonObject>();
    sensorObj["id"] = sensor.id;
    sensorObj["name"] = sensor.name;
    sensorObj["type"] = sensor.type;
    sensorObj["pin"] = sensor.pin;
    sensorObj["enabled"] = sensor.enabled;
    sensorObj["online"] = sensor.online;
    sensorObj["lastUpdate"] = sensor.lastUpdate;
    
    JsonObject readingsObj = sensorObj["readings"].to<JsonObject>();
    for (auto& reading : sensor.readings) {
      JsonObject readingObj = readingsObj[reading.first].to<JsonObject>();
      readingObj["value"] = reading.second.value;
      readingObj["unit"] = reading.second.unit;
      readingObj["timestamp"] = reading.second.timestamp;
    }
  }
  
  return doc;
}

int SensorManager::getOnlineSensorCount() {
  int count = 0;
  for (auto& sensor : sensors) {
    if (sensor.online && sensor.enabled) {
      count++;
    }
  }
  return count;
}

int SensorManager::getTotalSensorCount() {
  return sensors.size();
}

bool SensorManager::isSensorOnline(String sensorId) {
  for (auto& sensor : sensors) {
    if (sensor.id == sensorId) {
      return sensor.online && sensor.enabled;
    }
  }
  return false;
}

JsonDocument SensorManager::getSensorConfig() {
  return getAllSensorData();
}

void SensorManager::saveConfigToFile() {
  File file = SPIFFS.open("/sensor_config.json", "w");
  if (file) {
    JsonDocument config = getSensorConfig();
    serializeJson(config, file);
    file.close();
  }
}

void SensorManager::loadConfigFromFile() {
  if (SPIFFS.exists("/sensor_config.json")) {
    File file = SPIFFS.open("/sensor_config.json", "r");
    if (file) {
      JsonDocument config;
      deserializeJson(config, file);
      file.close();
      
      sensors.clear();
      JsonArray sensorArray = config["sensors"];
      for (JsonObject sensorObj : sensorArray) {
        Sensor sensor;
        sensor.id = sensorObj["id"].as<String>();
        sensor.name = sensorObj["name"].as<String>();
        sensor.type = sensorObj["type"].as<String>();
        sensor.pin = sensorObj["pin"];
        sensor.enabled = sensorObj["enabled"];
        sensor.online = false; // Will be updated when sensors are read
        sensor.lastUpdate = 0;
        
        sensors.push_back(sensor);
      }
    }
  }
}
