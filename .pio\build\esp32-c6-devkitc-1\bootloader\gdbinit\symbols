# Load esp32c6 ROM ELF symbols
define target hookpost-remote
set confirm off
  # if $_streq((char *) 0x4004a798, "Sep 19 2022")
  if (*(int*) 0x4004a798) == 0x20706553 && (*(int*) 0x4004a79c) == 0x32203931 && (*(int*) 0x4004a7a0) == 0x323230
    add-symbol-file C:/Users/<USER>/.platformio/packages/tool-esp-rom-elfsesp32c6_rev0_rom.elf
  else
    echo Warning: Unknown esp32c6 ROM revision.\n
  end
set confirm on
end


# Load bootloader symbols
set confirm off
    # Bootloader elf was not found
set confirm on

# Load application symbols
file C:/Users/<USER>/Desktop/Projects/ESP32/ESP32-C6-DevKitC-1_v1.2/temp_monitor_w_webserver/.pio/build/esp32-c6-devkitc-1/bootloader/bootloader.elf
